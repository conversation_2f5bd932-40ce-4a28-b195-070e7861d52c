import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import { ContactDocument } from "./schema/contact.schema";
import { CreateContactDto } from "./dto/create-contact.dto";
import { GetContactDto } from "./dto/get-contact.dto";
import { ClientDocument } from "src/client/schema/client.schema";
import { ContactTypeEnum } from "./enum/contact.enum";
import { AddLinkedContactDto } from "./dto/add-linked-contact.dto";
import OkResponse from "src/shared/http/response/ok.http";
import {
    buildMongoQuery,
    getTextChange,
    dedupeObjects,
    dedupeArray,
    dynamicSort,
} from "src/shared/helpers/logics";
import { randomUUID } from "crypto";
import { UpdateDndDto } from "./dto/update-dnd.dto";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { PriceDocument } from "src/project/schema/price-schema";
import { ProjectDocument } from "src/project/schema/project.schema";
import { GetSearchContactDto } from "./dto/search.dto";
import { LeadDocument } from "src/lead/schema/lead.schema";
import { CreateContactCommentDto } from "./dto/create-contact-comment.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import { CreateNewActionDto } from "./dto/create-new-action.dto";
import { UpdateContactActivityDto } from "./dto/update-contact-activity.dto";
import { ActivityLogDocument } from "src/activity-log/schema/activity-log.schema";
import { OpportunityActivityDocument } from "src/opportunity/schema/opportunity-activity-log.schema";
import { LostAndUnLostContactDto } from "./dto/lost-contact.dto";
import { ReferrersDocument } from "src/company/schema/referrers.schema";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { MergeContactsDto } from "./dto/merge-contacts.dto";
import { IsFullWidth } from "class-validator";

@Injectable()
export class ContactsService {
    constructor(
        @InjectModel("Contact") private contactModel: Model<ContactDocument>,
        @InjectModel("Client") private clientModel: Model<ClientDocument>,
        @InjectModel("Opportunity") private opportunityModel: Model<OpportunityDocument>,
        @InjectModel("Project") private projectModel: Model<ProjectDocument>,
        @InjectModel("Price") private priceModel: Model<PriceDocument>,
        @InjectModel("Lead") private leadModel: Model<LeadDocument>,
        @InjectModel("ActivityLog") private activityLogModel: Model<ActivityLogDocument>,
        @InjectModel("OpportunityActivity")
        private opportunityActivityModel: Model<OpportunityActivityDocument>,
        @InjectModel("Referrers") private readonly referrersModel: Model<ReferrersDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectConnection() private readonly connection: Connection,
    ) {
        // this.migrateClientToContact();
        // this.migrateLeadToContact();
        // this.migrateReferrerToContact();
        // this.migrateOpportunityClientIdToContactId();
        // this.migrateOpportunityActivity();
        ////////////
        // this.migrateTrackingAttribution();
        // this.migrateBusinessName();
        // this.checkLeadForContact();
        // this.migrateLeadToContact2();
        // this.migrateLeadStage();
        // this.fixIncorrectContactIdAssignments();
        // this.fixNewLeadDate();
        // this.migrateContactTypesByOpportunity(false); // Set to false to apply changes
        // this.migrateInvalidLeadReasonToObjectStructure();
        // this.migratePhoneNumbers(false); // Set to false to apply changes
        // this.createLeadsForOppsMigration();
        // this.migrateContactCounts();
        ////////////
        // this.findAll(
        //     {
        //         filter: {
        //             filter: [
        //                 { field: "type", operator: "has_duplicates", value: "businessName" },
        //                 // { field: "type", operator: "is_not", value: "lead" },
        //                 // { field: "type", operator: "is_not", value: "client" },
        //             ],
        //             logic: "AND",
        //         },
        //     },
        //     { companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3" },
        // );
        // this.getLinkedContact("f8486cee-7726-4f82-bb14-bde9a351ad38", {
        //     companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
        // });
        // this.getContactOpportunities("040cc053-5692-471f-869f-7fa2927cfd16", {
        //     companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
        // });
    }

    async create(createContactDto: CreateContactDto, user) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            let activityBody = `created a New Contact`;
            const contactId = randomUUID();
            let leadsCount = 0;

            if (createContactDto.type === ContactTypeEnum.LEAD) {
                createContactDto.status = "active";
                leadsCount = 1;
                const stageData = await this.crmStageModel.findOne(
                    {
                        companyId: user.companyId,
                        deleted: false,
                        stageGroup: StageGroupEnum.Leads,
                        sequence: 1,
                    },
                    { _id: 1, defaultCsrId: 1 },
                );
                const stageId = stageData._id;
                const csrId = createContactDto?.csrId || stageData.defaultCsrId;
                if (!createContactDto["newLeadDate"] || createContactDto["newLeadDate"] === undefined)
                    createContactDto["newLeadDate"] = new Date();
                activityBody = `created a New Lead`;
                // createContactDto.newLeadDate = new Date();

                // create lead
                const createdLead = new this.leadModel({
                    contactId,
                    companyId: user.companyId,
                    createdBy: user.memberId,
                    status: "active",
                    stageId,
                    csrId,
                    ...createContactDto,
                });
                await createdLead.save({ session });
            }
            const newContact = new this.contactModel({
                _id: contactId,
                ...createContactDto,
                companyId: user.companyId,
                createdBy: user.memberId,
                dateReceived: new Date(),
                leadsCount,
                opportunitiesCount: 0,
                referralsCount: 0,
            });
            await newContact.save({ session });

            // creating activity
            const createdContactActivity = new this.activityLogModel({
                moduleId: contactId,
                moduleType: "contact",
                companyId: user.companyId,
                activities: [
                    {
                        _id: randomUUID(),
                        body: activityBody,
                        createdBy: user.memberId,
                        createdAt: new Date().toISOString(),
                    },
                ],
            });

            await createdContactActivity.save({ session });
            await session.commitTransaction();

            return new OkResponse({
                id: contactId,
                contact: newContact,
                message: "Contact created successfully",
            });
        } catch (error: any) {
            await session.abortTransaction();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }

    async findAll(getContactDto: GetContactDto, user) {
        try {
            const { limit = 10, skip = 1, search, filter, deleted = false } = getContactDto;
            const skipValue = (skip - 1) * limit;
            const fFilter = filter ? JSON.parse(filter) : null;

            // Ensure the filter includes companyId and deleted status
            const mongoQuery = fFilter ? buildMongoQuery(fFilter) : {};

            // Check if any field has duplicate detection enabled
            const duplicateFields = Object.keys(mongoQuery).filter(
                (key) => mongoQuery[key] && mongoQuery[key].__duplicateField,
            );

            const cleanFilters: any = {};
            const duplicateConditions: any = {};
            const countFilters: any = {};

            Object.keys(mongoQuery).forEach((key) => {
                if (mongoQuery[key].__duplicateField) {
                    // This is a duplicate field - prepare for aggregation
                    duplicateConditions[key] = { $exists: true, $nin: [null, ""] };
                } else if (this.isCountField(key, mongoQuery[key])) {
                    // This is a count-based filter for related collections
                    countFilters[key] = mongoQuery[key];
                } else {
                    // Regular filter
                    cleanFilters[key] = mongoQuery[key];
                }
            });

            // Build aggregation pipeline for duplicate detection and count filters
            const pipeline: any[] = [
                // Match base filters and duplicate conditions
                {
                    $match: {
                        companyId: user.companyId,
                        deleted,
                        ...cleanFilters,
                        ...duplicateConditions,
                    },
                },
            ];

            // Add count-based lookups if needed
            if (Object.keys(countFilters).length > 0) {
                this.addCountFiltersToPipeline(pipeline, countFilters, user.companyId);
            }
            console.log("pipeline", pipeline);

            const sortObj = {};

            // For each duplicate field, group and filter for duplicates
            duplicateFields.forEach((field) => {
                sortObj[field] = 1;
                pipeline.push(
                    // Group by the duplicate field
                    {
                        $group: {
                            _id: `$${field}`,
                            count: { $sum: 1 },
                            contacts: { $push: "$$ROOT" },
                        },
                    },
                    // Only keep groups with more than 1 contact (duplicates)
                    {
                        $match: {
                            count: { $gt: 1 },
                        },
                    },
                    // Unwind to get individual contacts back
                    {
                        $unwind: "$contacts",
                    },
                    // Replace root with the contact document
                    {
                        $replaceRoot: { newRoot: "$contacts" },
                    },
                );
            });

            sortObj["createdAt"] = -1;

            // Add search filter if provided
            if (search) {
                pipeline.push({
                    $match: {
                        $or: [
                            { fullName: { $regex: search, $options: "i" } },
                            { businessName: { $regex: search, $options: "i" } },
                            { firstName: { $regex: search, $options: "i" } },
                            { lastName: { $regex: search, $options: "i" } },
                            { street: { $regex: search, $options: "i" } },
                            { city: { $regex: search, $options: "i" } },
                            { state: { $regex: search, $options: "i" } },
                            { zip: { $regex: search, $options: "i" } },
                            { phone: { $regex: search, $options: "i" } },
                            { email: { $regex: search, $options: "i" } },
                        ],
                    },
                });
            }

            // Sort by creation date
            pipeline.push(
                {
                    $lookup: {
                        from: "LeadSource",
                        localField: "leadSourceId",
                        foreignField: "_id",
                        as: "leadSource",
                        pipeline: [{ $project: { name: 1, comapnyId: 1, createdBy: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$leadSource",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "Campaign",
                        localField: "campaignId",
                        foreignField: "_id",
                        as: "campaign",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                {
                    $addFields: {
                        fullName: { $cond: [{ $eq: ["$isBusiness", true] }, "$businessName", "$fullName"] },
                    },
                },
                {
                    $unwind: {
                        path: "$campaign",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                { $sort: sortObj },
                {
                    $project: {
                        fullName: 1,
                        businessName: 1,
                        isBusiness: 1,
                        phone: 1,
                        email: 1,
                        type: 1,
                        street: 1,
                        city: 1,
                        state: 1,
                        zip: 1,
                        leadSourceName: "$leadSource.name",
                        campaignName: "$campaign.name",
                        createdAt: 1,
                        dateReceived: 1,
                        firstName: 1,
                        lastName: 1,
                        fullAddress: 1,
                        nextAction: 1,
                        tags: 1,
                    },
                },
                {
                    $facet: {
                        paginatedResults: [{ $skip: skipValue }, { $limit: limit }],
                        totalCount: [{ $count: "count" }],
                    },
                },
            );

            // Execute aggregation
            const result = await this.contactModel.aggregate(pipeline, {
                allowDiskUse: true,
                maxTimeMS: 300000, // Set appropriate timeout
            });
            const paginatedResults = result[0]?.paginatedResults;
            const totalCount = result[0]?.totalCount[0]?.count || 0;

            return new OkResponse({
                data: paginatedResults,
                pagination: {
                    page: skip,
                    limit,
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Check if a field requires counting related documents
     */
    private isCountField(field: string, filterValue: any): boolean {
        const countFields = ["opportunities", "referrer", "leads"];
        const countOperators = ["$gt", "$lt", "$gte", "$lte"];

        return (
            countFields.includes(field) &&
            typeof filterValue === "object" &&
            Object.keys(filterValue).some((op) => countOperators.includes(op))
        );
    }

    /**
     * Add count-based filters to aggregation pipeline using stored count fields
     */
    private addCountFiltersToPipeline(pipeline: any[], countFilters: any, companyId: string): void {
        const matchConditions: any = {};

        // Use stored count fields instead of expensive lookups
        if (countFilters?.opportunities) {
            matchConditions.opportunitiesCount = countFilters.opportunities;
        }

        if (countFilters?.referrer) {
            matchConditions.referralsCount = countFilters.referrer;
        }

        if (countFilters?.leads) {
            matchConditions.leadsCount = countFilters.leads;
        }

        // Add a single match stage with all count conditions
        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({
                $match: matchConditions,
            });
        }
    }

    /**
     * Update contact count fields when related data changes
     */
    async updateContactCounts(contactId: string, companyId: string): Promise<void> {
        try {
            const [opportunitiesCount, referralsOppCount, leadsCount, referralLeadsCount] = await Promise.all(
                [
                    // Count opportunities for this contact
                    this.opportunityModel.countDocuments({
                        contactId,
                        companyId,
                        deleted: false,
                    }),
                    // Count opportunities where this contact is the referrer
                    this.opportunityModel.countDocuments({
                        referredBy: contactId,
                        companyId,
                        deleted: false,
                        status: { $in: ["active", "lost"] },
                    }),
                    // Count leads for this contact
                    this.leadModel.countDocuments({
                        contactId,
                        companyId,
                        deleted: false,
                    }),
                    this.leadModel.countDocuments({
                        referredBy: contactId,
                        companyId,
                        deleted: false,
                        status: { $in: ["active", "lost"] },
                    }),
                ],
            );

            await this.contactModel.updateOne(
                { _id: contactId, companyId },
                {
                    $set: {
                        opportunitiesCount,
                        referralsCount: referralsOppCount + referralLeadsCount,
                        leadsCount,
                    },
                },
            );
        } catch (error) {
            console.error(`Error updating contact counts for ${contactId}:`, error);
        }
    }

    /**
     * Update multiple contacts' count fields efficiently
     */
    async updateMultipleContactCounts(contactIds: string[], companyId: string): Promise<void> {
        try {
            const updatePromises = contactIds.map((contactId) =>
                this.updateContactCounts(contactId, companyId),
            );
            await Promise.all(updatePromises);
        } catch (error) {
            console.error("Error updating multiple contact counts:", error);
        }
    }

    /**
     * Increase contact count fields when related data is added
     */
    async increaseContactCounts(
        contactId: string,
        companyId: string,
        fields: Record<string, number>,
    ): Promise<void> {
        try {
            const incrementObject = Object.entries(fields).reduce((acc, [field, value]) => {
                acc[field] = value;
                return acc;
            }, {});

            await this.contactModel.updateOne(
                { _id: contactId, companyId },
                {
                    $inc: incrementObject,
                },
            );
        } catch (error) {
            console.error(`Error increasing contact counts for ${contactId}:`, error);
        }
    }

    async findOne(id: string, user) {
        try {
            const [oppsComments, contact] = await Promise.all([
                this.opportunityModel.aggregate([
                    { $match: { contactId: id, companyId: user.companyId } },
                    {
                        $lookup: {
                            from: "CrmStage",
                            localField: "stage",
                            foreignField: "_id",
                            pipeline: [{ $project: { stageGroup: 1 } }],
                            as: "stageData",
                        },
                    },
                    { $unwind: { path: "$stageData", preserveNullAndEmptyArrays: true } },
                    {
                        $lookup: {
                            from: "Member",
                            let: {
                                memberArr: {
                                    $ifNull: [
                                        {
                                            $map: {
                                                input: { $ifNull: ["$comments", []] },
                                                as: "comment",
                                                in: "$$comment.createdBy",
                                            },
                                        },
                                        [],
                                    ],
                                },
                            },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $in: ["$_id", "$$memberArr"] },
                                    },
                                },
                                { $project: { _id: 1, name: 1 } },
                            ],
                            as: "users",
                        },
                    },
                    {
                        $project: {
                            oppId: "$_id",
                            comments: 1,
                            PO: 1,
                            num: 1,
                            stageGroup: "$stageData.stageGroup",
                            nextAction: 1,
                            users: 1,
                        },
                    },
                ]),
                this.contactModel
                    .aggregate([
                        {
                            $match: {
                                _id: id,
                                companyId: user.companyId,
                            },
                        },
                        {
                            $lookup: {
                                from: "Contact",
                                let: { referredBy: "$referredBy" },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $eq: ["$_id", "$$referredBy"] },
                                        },
                                    },
                                    { $project: { name: 1 } },
                                ],
                                as: "referrer",
                            },
                        },
                        {
                            $unwind: {
                                path: "$referrer",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Member",
                                let: {
                                    memberArr: {
                                        $ifNull: [
                                            {
                                                $map: {
                                                    input: { $ifNull: ["$comments", []] },
                                                    as: "comment",
                                                    in: "$$comment.createdBy",
                                                },
                                            },
                                            [],
                                        ],
                                    },
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $in: ["$_id", "$$memberArr"] },
                                        },
                                    },
                                    { $project: { _id: 1, name: 1 } },
                                ],
                                as: "users",
                            },
                        },
                    ])
                    .then((res) => res[0]),
            ]);

            const commentsFromOpps =
                oppsComments?.flatMap((opp) =>
                    (opp.comments || []).map((comment) => ({
                        oppId: opp.oppId,
                        ...comment,
                        PO: opp.PO,
                        num: opp.num,
                        stageGroup: opp.stageGroup,
                    })),
                ) || [];
            contact.oppsComments = commentsFromOpps;
            contact.oppnextAction = oppsComments?.map((opp) => opp.nextAction);

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            const allUsers = new Map();
            for (const user of contact.users ?? []) {
                allUsers.set(user._id, user);
            }

            // Add oppsComments.users (each opp from oppsComments array has `users`)
            for (const opp of oppsComments) {
                for (const user of opp.users ?? []) {
                    allUsers.set(user._id, user);
                }
            }
            const usersMap = allUsers;
            // Modify comments for name
            if (contact && contact.comments && contact.comments.length > 0) {
                contact.comments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                });
            }
            if (contact.oppsComments && contact.oppsComments.length > 0) {
                contact.oppsComments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                    contact.comments.push(c);
                });
            }

            return new OkResponse({ contact });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // find contact by id with limited data only (internal use)
    async findOneWithLimitedData(id: string, user, fields?: object) {
        try {
            const project =
                fields && Object.keys(fields).length > 0
                    ? fields
                    : {
                          fullName: 1,
                          businessName: 1,
                          isBusiness: 1,
                      };

            return await this.contactModel.findOne(
                {
                    _id: id,
                    companyId: user.companyId,
                },
                project,
            );
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async update(id: string, updateContactDto: CreateContactDto, user) {
        try {
            const contact = await this.contactModel.findOneAndUpdate(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $set: updateContactDto,
                },
                { new: true },
            );

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ contact });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async remove(id: string, user) {
        try {
            // checks before deleteing contact
            const opps = await this.opportunityModel.find({
                contactId: id,
                companyId: user.companyId,
                deleted: false,
            });
            if (opps.length > 0)
                throw new BadRequestException("Can't delete Contact! Contains active Opportunities");

            // delete contact
            const [contact] = await Promise.all([
                this.contactModel.findOneAndUpdate(
                    {
                        _id: id,
                        companyId: user.companyId,
                        deleted: false,
                    },
                    {
                        $set: { deleted: true },
                    },
                    { new: true },
                ),
                // delete leads
                await this.leadModel.updateMany(
                    {
                        contactId: id,
                        companyId: user.companyId,
                        deleted: false,
                    },
                    {
                        $set: { deleted: true },
                    },
                ),
            ]);

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ message: "Contact deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeMultipleContacts(ids: string[], user) {
        try {
            const contactsWithOpps = [];
            const contactsToDelete = [];

            // Check each contact for active opportunities
            for (const id of ids) {
                const opps = await this.opportunityModel.find({
                    contactId: id,
                    companyId: user.companyId,
                    deleted: false,
                });

                if (opps.length > 0) {
                    contactsWithOpps.push(id);
                } else {
                    contactsToDelete.push(id);
                }
            }

            if (contactsToDelete.length === 0) {
                return new OkResponse({
                    message: "No contacts could be deleted due to active opportunities",
                    undeleted: contactsWithOpps,
                });
            }

            // Delete contacts that don't have active opportunities
            const [contactResult, leadResult] = await Promise.all([
                this.contactModel.updateMany(
                    {
                        _id: { $in: contactsToDelete },
                        companyId: user.companyId,
                        deleted: false,
                    },
                    {
                        $set: { deleted: true },
                    },
                ),
                this.leadModel.updateMany(
                    {
                        contactId: { $in: contactsToDelete },
                        companyId: user.companyId,
                        deleted: false,
                    },
                    {
                        $set: { deleted: true },
                    },
                ),
            ]);

            const message =
                contactsWithOpps.length > 0
                    ? `${contactResult.modifiedCount} contacts deleted. ${contactsWithOpps.length} contacts could not be deleted due to active opportunities.`
                    : `${contactResult.modifiedCount} contacts deleted successfully`;

            return new OkResponse({
                message,
                deleted: contactsToDelete,
                undeleted: contactsWithOpps,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDelete(id: string, user) {
        try {
            // Check for active opportunities
            const opps = await this.opportunityModel.find({
                contactId: id,
                companyId: user.companyId,
                deleted: false,
            });

            if (opps.length > 0) {
                throw new BadRequestException("Cannot permanently delete contact with active opportunities");
            }

            const [contact, lead] = await Promise.all([
                this.contactModel.findOneAndDelete({
                    _id: id,
                    companyId: user.companyId,
                    deleted: true,
                }),
                this.leadModel.deleteMany({
                    contactId: id,
                    companyId: user.companyId,
                }),
            ]);

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({
                message: "Contact and associated leads permanently deleted",
                deletedContact: contact._id,
                deletedLeads: lead.deletedCount,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restore(id: string, user) {
        try {
            const contact = await this.contactModel.findOneAndUpdate(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: true,
                },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ message: "Contact restored successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreMultipleContacts(ids: string[], user) {
        try {
            const result = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: true,
                },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to restore all contacts");
            }

            return new OkResponse({ message: "Contacts restored successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addLinkedContact(id: string, linkedContactDtos: AddLinkedContactDto[], user) {
        try {
            const linkedContacts = linkedContactDtos.map((dto) => ({
                id: dto.id,
                relationship: dto.relationship,
            }));

            const { modifiedCount } = await this.contactModel.updateOne(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $push: {
                        linkedContacts: { $each: linkedContacts },
                    },
                },
            );

            if (!modifiedCount) {
                throw new BadRequestException("Failed to add linked contacts");
            }

            return new OkResponse({ message: "Linked contacts added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLinkedContact(id: string, user) {
        try {
            const [contact, relatedContacts] = await Promise.all([
                this.contactModel
                    .findOne(
                        {
                            _id: id,
                            companyId: user.companyId,
                            deleted: false,
                        },
                        { linkedContacts: 1 },
                    )
                    .populate(
                        "linkedContacts.id",
                        "fullName phone email notes fullAddress street city state zip businessName isBusiness",
                        "Contact",
                    ),
                this.contactModel
                    .find({
                        "linkedContacts.id": id,
                        companyId: user.companyId,
                        deleted: false,
                    })
                    .select("fullName phone email notes fullAddress street city state zip"),
            ]);

            // Combine both direct and reverse linked contacts
            const allLinkedContacts = [
                ...(contact?.linkedContacts || []).map((lc: any) => {
                    if (lc.id && typeof lc.id === "object" && lc.id !== null) {
                        return Object.assign({}, lc, {
                            id: Object.assign({}, lc.id, {
                                fullName: lc.id.isBusiness ? lc.id.businessName : lc.id.fullName,
                            }),
                        });
                    }
                    return lc;
                }),
                ...relatedContacts.map((c) => ({
                    id: {
                        _id: c._id,
                        fullName: c.isBusiness ? c.businessName : c.fullName,
                        phone: c.phone,
                        email: c.email,
                        notes: c.notes,
                        fullAddress: c.fullAddress,
                        street: c.street,
                        city: c.city,
                        state: c.state,
                        zip: c.zip,
                    },
                    relationship: "Parent Contact",
                })),
            ];

            return new OkResponse({ linkedContacts: allLinkedContacts });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeLinkedContact(id: string, linkedContactId: string, user) {
        try {
            const { modifiedCount } = await this.contactModel.updateOne(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $pull: {
                        linkedContacts: { id: linkedContactId },
                    },
                },
            );

            if (!modifiedCount) {
                throw new BadRequestException("Failed to remove linked contact");
            }

            return new OkResponse({ message: "Linked contact removed successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async searchContacts(dto: GetSearchContactDto, user) {
        try {
            const { limit = 10, skip = 1, type, search, fields, id } = dto;
            const skipValue = (skip - 1) * limit;

            const searchRegex = new RegExp(search, "i");
            const project = fields
                ? JSON.parse(fields)
                : {
                      fullName: 1,
                  };

            const query = {
                companyId: user.companyId,
                deleted: false,
                ...(type && { type }),
                ...(id && { _id: id }),
                $or: [
                    { firstName: searchRegex },
                    { lastName: searchRegex },
                    { fullName: searchRegex },
                    { businessName: searchRegex },
                    { phone: searchRegex },
                    { email: searchRegex },
                ],
            };

            const contacts = await this.contactModel.aggregate([
                { $match: query },
                {
                    $addFields: {
                        fullName: {
                            $cond: [
                                "$isBusiness",
                                {
                                    $concat: [
                                        { $ifNull: ["$businessName", ""] },
                                        { $cond: ["$fullName", { $concat: [" (", "$fullName", ")"] }, ""] },
                                    ],
                                },
                                "$fullName",
                            ],
                        },
                    },
                },
                { $project: { ...project } },
                { $sort: { createdAt: -1 } },
                { $skip: skipValue },
                { $limit: limit },
            ]);
            // console.log("contacts", contacts);
            return new OkResponse({ contacts });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateDnd(id: string, updateDndDto: UpdateDndDto, user) {
        try {
            const { modifiedCount } = await this.contactModel.updateOne(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $set: { dnd: updateDndDto },
                },
            );

            if (!modifiedCount) {
                throw new BadRequestException("Failed to update DND settings");
            }

            return new OkResponse({ message: "DND settings updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addTagsInMultipleContacts(ids: string[], tags: string[], user) {
        try {
            const { modifiedCount } = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $addToSet: {
                        tags: { $each: tags },
                    },
                },
            );

            if (modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to add tags to all contacts");
            }

            return new OkResponse({ message: "Tags added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeTagsInMultipleContacts(ids: string[], tags: string[], user) {
        try {
            const { modifiedCount } = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $pull: {
                        tags: { $in: tags },
                    },
                },
            );

            if (modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to remove tags from all contacts");
            }

            return new OkResponse({ message: "Tags removed successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getContactOpportunities(contactId: string, user) {
        try {
            const [contact, relatedContacts] = await Promise.all([
                this.contactModel.findOne(
                    {
                        _id: contactId,
                        companyId: user.companyId,
                        deleted: false,
                    },
                    { linkedContacts: 1 },
                ),
                this.contactModel.find(
                    {
                        linkedContacts: { $elemMatch: { id: contactId } },
                        companyId: user.companyId,
                        deleted: false,
                    },
                    { linkedContacts: 1 },
                ),
            ]);

            // Collect all relevant contact IDs
            const contactIds = [
                contactId,
                ...relatedContacts.map((c) => c._id),
                ...(contact?.linkedContacts?.map((c) => c.id) || []), // Safe access for linkedContacts
            ].filter((id, index, self) => id && self.indexOf(id) === index); // Remove duplicates

            const clientOpps = await this.opportunityModel.aggregate([
                {
                    $match: {
                        companyId: user.companyId,
                        contactId: { $in: contactIds },
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "ProjectType",
                        localField: "oppType",
                        foreignField: "_id",
                        as: "oppType",
                    },
                },
                { $unwind: { path: "$oppType", preserveNullAndEmptyArrays: true } },

                {
                    $lookup: {
                        from: "CrmStage",
                        localField: "stage",
                        foreignField: "_id",
                        as: "stage",
                    },
                },
                { $unwind: { path: "$stage", preserveNullAndEmptyArrays: true } },

                {
                    $lookup: {
                        from: "Contact",
                        localField: "contactId",
                        foreignField: "_id",
                        as: "contactId",
                    },
                },
                { $unwind: { path: "$contactId", preserveNullAndEmptyArrays: true } },

                {
                    $project: {
                        _id: 1,
                        PO: 1,
                        num: 1,
                        companyId: 1,
                        firstName: 1,
                        lastName: 1,
                        street: 1,
                        city: 1,
                        state: 1,
                        soldValue: 1,
                        realRevValue: 1,
                        contactId: 1,
                        stage: 1,
                        oppDate: 1,
                        oppType: { _id: "$oppType._id", name: "$oppType.name" },
                        fullName: {
                            $cond: {
                                if: { $eq: ["$contactId.isBusiness", true] },
                                then: "$contactId.businessName",
                                else: "$contactId.fullName",
                            },
                        },
                    },
                },
                { $sort: { createdAt: -1 } },
            ]);

            return new OkResponse({ clientOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async migrateContactAndOpp(mergeContactDto: MergeContactsDto, toContact: string, user) {
        if (!mergeContactDto.fromContact || mergeContactDto.fromContact.length === 0) {
            const body = `Attempted to create duplicate contact with:
      First Name: ${mergeContactDto.firstName || ""}
      Last Name: ${mergeContactDto.lastName || ""}
      Phone: ${mergeContactDto.phone || ""}`;
            await this.contactModel.updateOne(
                { _id: toContact },
                {
                    $push: {
                        comments: {
                            _id: randomUUID(),
                            body,
                            createdBy: user.memberId,
                            createdAt: new Date(),
                            edits: [],
                        },
                    },
                },
            );
            const updatableFields = [
                "businessName",
                "fullName",
                "firstName",
                "lastName",
                "phone",
                "email",
                "street",
                "city",
                "state",
                "zip",
                "fullAddress",
                "type",
                "isBusiness",
                "notes",
                "nextAction",
            ];
            const updateFields: any = {};
            for (const field of updatableFields) {
                if (Object.prototype.hasOwnProperty.call(mergeContactDto, field)) {
                    updateFields[field] = mergeContactDto[field];
                }
            }
            if (Object.keys(updateFields).length > 0) {
                await this.contactModel.updateOne({ _id: toContact }, { $set: updateFields });
            }
            await this.updateContactActivity(user.companyId, user.memberId, {
                id: toContact,
                body: body,
                currDate: new Date(),
            });

            // Fetch the updated contact with all details (excluding specific fields)
            const updatedContact = await this.contactModel
                .findOne({
                    _id: toContact,
                    companyId: user.companyId,
                })
                .select({
                    actions: 0,
                    automations: 0,
                    comments: 0,
                    dnd: 0,
                    linkedContacts: 0,
                    tags: 0,
                    tracking: 0,
                    referralsCount: 0,
                    selfGen: 0,
                    orders: 0,
                    opportunitiesCount: 0,
                    notes: 0,
                    leadCount: 0,
                })
                .lean();

            return new CreatedResponse({
                message: "Contacts merged successfully!",
                contact: updatedContact,
            });
        }

        const session = await this.connection.startSession();
        session.startTransaction();

        try {
            // Fetch all fromContacts and the toContact
            const fromContacts = await this.contactModel.find({
                companyId: user.companyId,
                _id: { $in: mergeContactDto.fromContact },
            });
            const newClient = await this.contactModel.findOne({ _id: toContact });

            if (!fromContacts.length || !newClient) {
                throw new BadRequestException(
                    !fromContacts.length ? "No fromContacts found." : "toContact not found.",
                );
            }

            // Mark all fromContacts as deleted
            const deleteResult = await this.contactModel.updateMany(
                { companyId: user.companyId, _id: { $in: mergeContactDto.fromContact } },
                { $set: { deleted: true } },
                { session },
            );
            // Update referredBy in opportunities and contacts
            await Promise.all([
                this.opportunityModel.updateMany(
                    { companyId: user.companyId, referredBy: { $in: mergeContactDto.fromContact } },
                    { $set: { referredBy: toContact } },
                    { session },
                ),
                this.contactModel.updateMany(
                    { companyId: user.companyId, referredBy: { $in: mergeContactDto.fromContact } },
                    { $set: { referredBy: toContact } },
                    { session },
                ),
            ]);

            // Migrate all related models (opportunity, lead, project, price) from all fromContacts to toContact
            const updateFilter = {
                companyId: user.companyId,
                contactId: { $in: mergeContactDto.fromContact },
            };
            const updateSet = { $set: { contactId: toContact } };
            const [oppRes, leadRes, projRes, priceRes] = await Promise.all([
                this.opportunityModel.updateMany(updateFilter, updateSet, { session }),
                this.leadModel.updateMany(updateFilter, updateSet, { session }),
                this.projectModel.updateMany(updateFilter, updateSet, { session }),
                this.priceModel.updateMany(updateFilter, updateSet, { session }),
            ]);

            // Merge all fields from all fromContacts into toContact
            let mergedNotes = newClient.notes || "";
            let mergedComments = [...(newClient.comments || [])];
            let mergedActions = [...(newClient.actions || [])];
            let mergedTags = [...(newClient.tags || [])];
            let mergedTracking = [...(newClient.tracking || [])];
            let allLinkedContacts = [...(newClient.linkedContacts || [])];

            for (const oldClient of fromContacts) {
                if (oldClient.notes) {
                    const now = new Date();
                    const mm = now.getMonth() + 1;
                    const dd = now.getDate();
                    const yy = now.getFullYear().toString().slice(-2);
                    const appendedNote = `From merged contact on ${mm}/${dd}/${yy}:\n${oldClient.notes}`;
                    mergedNotes = mergedNotes ? `${mergedNotes}\n${appendedNote}` : appendedNote;
                }
                mergedComments = mergedComments.concat(oldClient.comments || []);
                mergedActions = mergedActions.concat(oldClient.actions || []);
                mergedTags = mergedTags.concat(oldClient.tags || []);
                mergedTracking = mergedTracking.concat(oldClient.tracking || []);
                allLinkedContacts = allLinkedContacts.concat(oldClient.linkedContacts || []);
            }

            mergedTags = dedupeArray(mergedTags);
            mergedTracking = dedupeObjects(mergedTracking);
            const linkedContactsMap = new Map();
            for (const lc of allLinkedContacts) {
                if (lc && lc.id) {
                    linkedContactsMap.set(lc.id.toString(), lc);
                }
            }
            const mergedLinkedContacts = Array.from(linkedContactsMap.values());

            mergedComments.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            mergedActions.sort(
                (a, b) =>
                    new Date(a.due || a.createdAt || 0).getTime() -
                    new Date(b.due || b.createdAt || 0).getTime(),
            );

            for (const oldClient of fromContacts) {
                const body = `Merged with ${oldClient.fullName} -> Phone: ${oldClient.phone}, Email: ${oldClient.email}, Address: ${oldClient.fullAddress}`;
                mergedComments.push({
                    _id: randomUUID(),
                    body,
                    createdBy: user.memberId,
                    createdAt: new Date(),
                    edits: [],
                });
            }

            // --- Merge fields: dateReceived and leadSourceId from oldest contact ---
            // Gather all contacts (fromContacts + toContact)
            const allMergedContacts = [...fromContacts, newClient];
            // Find the contact with the oldest dateReceived
            let oldestContact = allMergedContacts[0];
            for (const c of allMergedContacts) {
                if (c.dateReceived && oldestContact.dateReceived) {
                    if (new Date(c.dateReceived) < new Date(oldestContact.dateReceived)) {
                        oldestContact = c;
                    }
                }
            }
            // Use oldest dateReceived and leadSourceId
            const mergedDateReceived = oldestContact.dateReceived;
            const mergedLeadSourceId = oldestContact.leadSourceId;

            // Use values from mergeDto for single-value fields (only if defined)
            const updateFields: any = {
                ...(mergeContactDto.businessName !== undefined && {
                    businessName: mergeContactDto.businessName,
                }),
                ...(mergeContactDto.fullName !== undefined && { fullName: mergeContactDto.fullName }),
                ...(mergeContactDto.firstName !== undefined && { firstName: mergeContactDto.firstName }),
                ...(mergeContactDto.lastName !== undefined && { lastName: mergeContactDto.lastName }),
                ...(mergeContactDto.phone !== undefined && { phone: mergeContactDto.phone }),
                ...(mergeContactDto.email !== undefined && { email: mergeContactDto.email }),
                ...(mergeContactDto.street !== undefined && { street: mergeContactDto.street }),
                ...(mergeContactDto.city !== undefined && { city: mergeContactDto.city }),
                ...(mergeContactDto.state !== undefined && { state: mergeContactDto.state }),
                ...(mergeContactDto.zip !== undefined && { zip: mergeContactDto.zip }),
                ...(mergeContactDto.fullAddress !== undefined && {
                    fullAddress: mergeContactDto.fullAddress,
                }),
                ...(mergeContactDto.type !== undefined && { type: mergeContactDto.type }),
                ...(mergeContactDto.isBusiness !== undefined && { isBusiness: mergeContactDto.isBusiness }),
                notes: mergedNotes,
                comments: mergedComments,
                actions: mergedActions,
                tracking: mergedTracking,
                tags: mergedTags,
                linkedContacts: mergedLinkedContacts,
                // Add merged dateReceived and leadSourceId
                ...(mergedDateReceived && { dateReceived: mergedDateReceived }),
                ...(mergedLeadSourceId && { leadSourceId: mergedLeadSourceId }),
            };
            // Set nextAction separately if present
            if (mergeContactDto.nextAction !== undefined) {
                updateFields.nextAction = mergeContactDto.nextAction;
            }

            // Update toContact with merged data and selected fields
            await this.contactModel.updateOne(
                { companyId: user.companyId, _id: toContact },
                { $set: updateFields },
                { session },
            );

            // Merge activity logs from all fromContacts into toContact
            const activityLogs = await this.activityLogModel.find({
                moduleId: { $in: mergeContactDto.fromContact.concat([toContact]) },
                moduleType: "contact",
            });
            let mergedActivities: any[] = [];
            for (const log of activityLogs) {
                if (Array.isArray(log.activities)) {
                    mergedActivities = mergedActivities.concat(log.activities);
                }
            }
            mergedActivities.sort(
                (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
            );

            // Update or create ActivityLog for the merged contact
            const toContactActivityLog = activityLogs.find(
                (log) => log.moduleId.toString() === toContact.toString(),
            );
            if (toContactActivityLog) {
                await this.activityLogModel.updateOne(
                    { moduleId: toContact, moduleType: "contact" },
                    { $set: { activities: mergedActivities } },
                    { session },
                );
            } else if (mergedActivities.length > 0) {
                await this.activityLogModel.create(
                    [
                        {
                            moduleId: toContact,
                            moduleType: "contact",
                            companyId: user.companyId,
                            activities: mergedActivities,
                        },
                    ],
                    { session },
                );
            }

            // --- EDGE CASES ---
            // Fetch all leads and opportunities for fromContacts and toContact
            const allContactIds = [...mergeContactDto.fromContact, toContact];
            const allLeads = await this.leadModel.find({
                contactId: { $in: allContactIds },
                companyId: user.companyId,
            });

            const allOpps = await this.opportunityModel.find({
                contactId: { $in: allContactIds },
                companyId: user.companyId,
            });

            // Group leads and opps by contactId
            const leadsByContact = {};
            allLeads.forEach((lead) => {
                if (!leadsByContact[lead.contactId]) leadsByContact[lead.contactId] = [];
                leadsByContact[lead.contactId].push(lead);
            });
            const oppsByContact = {};
            allOpps.forEach((opp) => {
                if (!oppsByContact[opp.contactId]) oppsByContact[opp.contactId] = [];
                oppsByContact[opp.contactId].push(opp);
            });

            // 1. Both contacts have active leads and no opportunities
            const allActiveLeads = allLeads.filter((l) => l.status === "active");
            if (allActiveLeads.length > 1) {
                // Sort by newLeadDate or createdAt (oldest first)
                allActiveLeads.sort((a, b) => {
                    const aDate = new Date(a.newLeadDate || a.createdAt).getTime();
                    const bDate = new Date(b.newLeadDate || b.createdAt).getTime();
                    return aDate - bDate;
                });
                // Mark all but the newest as lost
                for (let i = 0; i < allActiveLeads.length - 1; i++) {
                    const lead = allActiveLeads[i];
                    await this.leadModel.updateOne(
                        { _id: lead._id },
                        { $set: { status: "lost", lostReason: "Other" } },
                        { session },
                    );
                }
            }

            // 2. One contact with active lead merged into another with active opportunity
            for (const fromId of mergeContactDto.fromContact) {
                const fromLeads = (leadsByContact[fromId] || []).filter((l) => l.status === "active");
                const toOpps = (oppsByContact[toContact] || []).filter((o) => o.status === "active");
                if (fromLeads.length && toOpps.length) {
                    for (const lead of fromLeads) {
                        for (const opp of toOpps) {
                            const leadDate = new Date(lead.newLeadDate || lead.createdAt).getTime();
                            const oppDate = new Date(opp.oppDate || opp.createdAt).getTime();
                            if (leadDate < oppDate) {
                                // Lead before opp: converted
                                await this.leadModel.updateOne(
                                    { _id: lead._id },
                                    {
                                        $set: {
                                            status: "converted",
                                            oppId: opp._id,
                                            convertedDate: new Date(),
                                        },
                                    },
                                    { session },
                                );
                            } else {
                                // Lead after opp: invalid
                                await this.leadModel.updateOne(
                                    { _id: lead._id },
                                    { $set: { status: "invalid", invalidLeadReason: "Current Client" } },
                                    { session },
                                );
                            }
                        }
                    }
                }
            }

            // --- EDGE CASES END ---

            // Cleanup: Remove merged (deleted) contacts from all other contacts' linkedContacts arrays
            if (mergeContactDto.fromContact && mergeContactDto.fromContact.length > 0) {
                // Remove links to merged contacts
                await this.contactModel.updateMany(
                    { "linkedContacts.id": { $in: mergeContactDto.fromContact } },
                    { $pull: { linkedContacts: { id: { $in: mergeContactDto.fromContact } } } },
                    { session },
                );

                // Remove any self-references from the target contact's linkedContacts
                await this.contactModel.updateOne(
                    { _id: toContact },
                    { $pull: { linkedContacts: { id: toContact } } },
                    { session },
                );

                // Transfer links to toContact (but avoid creating self-references)
                for (const fromId of mergeContactDto.fromContact) {
                    // Find all contacts that had fromId as a linked contact
                    const contactsWithLink = await this.contactModel.find({ "linkedContacts.id": fromId });
                    for (const c of contactsWithLink) {
                        // Skip if this contact is the target contact (to avoid self-references)
                        if (c._id.toString() === toContact.toString()) {
                            continue;
                        }

                        // Find the relationship type(s)
                        const links = c.linkedContacts.filter((lc) => lc.id === fromId);
                        for (const link of links) {
                            // Only add if not already present and not creating a self-reference
                            if (!c.linkedContacts.some((lc) => lc.id === toContact)) {
                                await this.contactModel.updateOne(
                                    { _id: c._id },
                                    {
                                        $push: {
                                            linkedContacts: {
                                                id: toContact,
                                                relationship: "Other",
                                            },
                                        },
                                    },
                                    { session },
                                );
                            }
                        }
                    }
                }
            }

            await session.commitTransaction();

            // Fetch the updated merged contact with all details
            const mergedContact = await this.contactModel
                .findOne({
                    _id: toContact,
                    companyId: user.companyId,
                })
                .select({
                    actions: 0,
                    automations: 0,
                    comments: 0,
                    dnd: 0,
                    linkedContacts: 0,
                    tags: 0,
                    tracking: 0,
                    referralsCount: 0,
                    selfGen: 0,
                    orders: 0,
                    opportunitiesCount: 0,
                    notes: 0,
                    leadCount: 0,
                })
                .lean();

            return new OkResponse({
                message: "Contacts merged and opportunities migrated successfully!",
                contact: mergedContact,
            });
        } catch (error: any) {
            await session.abortTransaction();
            throw error instanceof HttpException ? error : new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }

    async updateContactStatus(contactId: string, newStatus: string) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $set: {
                        status: newStatus,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Lead status changed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getImportedContacts(companyId: string, paginationDto: PaginationDto) {
        try {
            const limit = paginationDto.limit || 10;
            const skipValue = (paginationDto.skip - 1) * limit;

            // Get contact IDs from leads and fetch contacts in a single aggregation pipeline
            const contacts = await this.leadModel.aggregate([
                {
                    $match: {
                        companyId,
                        zapierLead: true,
                        rawTracking: { $exists: true, $ne: {}, $type: "object" },
                    },
                },
                { $project: { contactId: 1, rawTracking: 1, createdAt: 1 } },
                {
                    $lookup: {
                        from: "Contact",
                        localField: "contactId",
                        foreignField: "_id",
                        pipeline: [
                            {
                                $project: {
                                    fullName: 1,
                                    email: 1,
                                    phone: 1,
                                    // createdAt: 1,
                                    businessName: 1,
                                    isBusiness: 1,
                                },
                            },
                        ],
                        as: "contact",
                    },
                },
                { $unwind: { path: "$contact", preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        contactName: {
                            $cond: [
                                { $eq: ["$contact.isBusiness", true] },
                                "$contact.businessName",
                                "$contact.fullName",
                            ],
                        },
                    },
                },
                { $sort: { createdAt: -1 } },
                {
                    $project: {
                        contactId: 1,
                        rawTracking: 1,
                        contactName: 1,
                        fullName: "$contact.fullName",
                        email: "$contact.email",
                        phone: "$contact.phone",
                        createdAt: 1,
                    },
                },
                {
                    $facet: {
                        paginatedResults: [{ $skip: skipValue }, { $limit: limit }],
                        totalCount: [{ $count: "count" }],
                    },
                },
            ]);
            const paginatedResults = contacts[0].paginatedResults;
            const totalCount = contacts[0].totalCount[0]?.count || 0;

            return new OkResponse({
                contacts: paginatedResults,
                pagination: {
                    page: paginationDto.skip,
                    limit,
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // comments section
    async addComment(contactId: string, createContactCommentDto: CreateContactCommentDto) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $push: {
                        comments: {
                            _id: randomUUID(),
                            body: createContactCommentDto.body,
                            createdBy: createContactCommentDto.memberId,
                            createdAt: new Date(createContactCommentDto.currDate),
                            edits: [],
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Comment created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateComment(user: any, contactId: string, commentId: string, body: any) {
        try {
            const { memberId, companyId } = user;
            const contact = await this.contactModel
                .findOne({
                    _id: contactId,
                    companyId,
                })
                .select("comments");

            // Use correct body string if body is an object (like opp comment update)
            const commentBody =
                typeof body === "object" && body !== null && "body" in body ? body.body : body;
            if (!contact) throw new HttpException("contact not found", HttpStatus.BAD_REQUEST);

            const memberComment = contact.comments.find((c) => c._id === commentId);

            if (memberComment && memberComment.createdBy !== memberId)
                throw new BadRequestException("You can only edit your comments");

            if (memberComment && memberComment?.edits?.length >= 5)
                throw new BadRequestException("Maximum numbers of edit limit reached");

            if (
                memberComment &&
                Math.abs(new Date(memberComment?.createdAt).getTime() - new Date().getTime()) > 60 * 60 * 1000
            )
                throw new BadRequestException("You can't edit old comment");

            const edits: any[] =
                commentBody !== memberComment.body
                    ? [{ editedAt: new Date(), edit: memberComment.body }, ...(memberComment?.edits || [])]
                    : [...(memberComment?.edits || [])];

            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $set: {
                        "comments.$[elem].body": commentBody,
                        "comments.$[elem].editedAt": new Date(),
                        "comments.$[elem].edits": edits,
                    },
                },
                {
                    arrayFilters: [{ "elem._id": commentId, "elem.createdBy": memberId }],
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Comment updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteComment(contactId: string, commentId: string, memberId: string) {
        try {
            const result = await this.contactModel.updateOne(
                {
                    _id: contactId,
                    comments: {
                        $elemMatch: {
                            _id: commentId,
                            createdBy: memberId,
                        },
                    },
                },
                {
                    $pull: {
                        comments: { _id: commentId },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Comment deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getComments(contactId: string) {
        try {
            const contact = await this.contactModel.aggregate([
                {
                    $match: {
                        _id: contactId,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "comments.createdBy",
                        foreignField: "_id",
                        as: "userDetails",
                    },
                },
                {
                    $project: {
                        comments: {
                            $map: {
                                input: "$comments",
                                as: "comment",
                                in: {
                                    $mergeObjects: [
                                        "$$comment",
                                        {
                                            name: {
                                                $let: {
                                                    vars: {
                                                        user: {
                                                            $arrayElemAt: [
                                                                {
                                                                    $filter: {
                                                                        input: "$userDetails",
                                                                        as: "user",
                                                                        cond: {
                                                                            $eq: [
                                                                                "$$user._id",
                                                                                "$$comment.createdBy",
                                                                            ],
                                                                        },
                                                                    },
                                                                },
                                                                0,
                                                            ],
                                                        },
                                                    },
                                                    in: {
                                                        $ifNull: ["$$user.name", "$$comment.createdBy"],
                                                    },
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                    },
                },
            ]);

            return new OkResponse({ comments: contact[0]?.comments || [] });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // actions
    async createNewAction(
        companyId: string,
        memberId: string,
        contactId: string,
        createNewActionDto: CreateNewActionDto,
    ) {
        try {
            // console.log(createNewActionDto);
            const result = await this.contactModel.updateOne(
                { _id: contactId, companyId },
                {
                    $set: {
                        nextAction: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            assignTo: createNewActionDto.assignTo,
                            createdBy: memberId,
                            createdAt: createNewActionDto.currDate,
                        },
                        // todoCheck: false,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Action created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async completeAction(
        companyId: string,
        memberId: string,
        contactId: string,
        createNewActionDto: CreateNewActionDto,
    ) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId, companyId, "actions._id": { $ne: createNewActionDto.id } }, // Ensures ID is not already in actions
                {
                    $push: {
                        actions: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            assignTo: createNewActionDto.assignTo,
                            completedBy: memberId,
                            completedAt: createNewActionDto.currDate,
                        },
                    },
                    // todoCheck: true,
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getActions(contactId: string) {
        try {
            const contact = await this.contactModel.findOne(
                { _id: contactId },
                { actions: 1, nextAction: 1 },
            );

            return new OkResponse({ actions: contact?.actions, nextAction: contact?.nextAction });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteAction(contactId: string, actionId: string) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $pull: {
                        actions: { _id: actionId },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateNextAction(contactId: string, nextAction: any) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $set: {
                        nextAction,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Next action updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // activity
    async updateContactActivity(
        companyId: string,
        memberId: string,
        updateContactActivityDto: UpdateContactActivityDto,
    ) {
        try {
            const activity = await this.activityLogModel.updateOne(
                { moduleId: updateContactActivityDto.id, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: updateContactActivityDto.body,
                            createdBy: memberId,
                            createdAt: updateContactActivityDto.currDate,
                        },
                    },
                },
                { upsert: true, new: true },
            );

            return new OkResponse({ data: activity, message: "Contact activity updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getContactActivity(contactId: string) {
        try {
            const allData = await this.activityLogModel.aggregate([
                { $match: { moduleId: contactId } },
                {
                    $lookup: {
                        from: "Member",
                        localField: "activities.createdBy",
                        foreignField: "_id",
                        as: "userDetails",
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyId: 1,
                        oppId: 1,
                        activities: {
                            $map: {
                                input: "$activities",
                                as: "act",
                                in: {
                                    $mergeObjects: [
                                        "$$act",
                                        {
                                            name: {
                                                $let: {
                                                    vars: {
                                                        user: {
                                                            $arrayElemAt: [
                                                                {
                                                                    $filter: {
                                                                        input: "$userDetails",
                                                                        as: "user",
                                                                        cond: {
                                                                            $eq: [
                                                                                "$$user._id",
                                                                                "$$act.createdBy",
                                                                            ],
                                                                        },
                                                                    },
                                                                },
                                                                0,
                                                            ],
                                                        },
                                                    },
                                                    in: {
                                                        $ifNull: ["$$user.name", "$$act.createdBy"],
                                                    },
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                        createdAt: 1,
                        updatedAt: 1,
                    },
                },
            ]);

            if (!allData.length) {
                return new OkResponse({ activities: [] });
            }

            return new OkResponse({ activities: allData[0].activities });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // lead
    async getContactOfTypeLeads(status: string, deleted: boolean, user) {
        try {
            const query = deleted
                ? {
                      companyId: user.companyId,
                      deleted,
                      type: ContactTypeEnum.LEAD,
                  }
                : {
                      companyId: user.companyId,
                      status,
                      deleted,
                      type: ContactTypeEnum.LEAD,
                  };
            const leads = await this.contactModel
                .find(query)
                .select("fullName businessName isBusiness csrId stageId newLeadDate createdAt");

            return new OkResponse({ leads });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLeadByContactId(contactId: string, companyId: string) {
        try {
            const leads = await this.leadModel.find({ contactId, companyId }).sort({ newLeadDate: -1 });
            return new OkResponse({ leads });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // migration scripts
    async migrateLeadToContact2() {
        try {
            console.log("Starting lead to contact migration");

            // await this.leadModel.updateMany({ stage: { $exists: true } }, { $rename: { stage: "stageId" } });

            console.log("Successfully migrated lead stage");

            // Find all leads with no contactId
            const leads = await this.leadModel.find({ contactId: { $exists: false } });
            const contacts = await this.contactModel.find({
                _id: { $in: leads.map((lead) => lead._id) },
                stageId: { $exists: true },
            });
            console.log(leads.length, contacts.length);

            const updatePromises = contacts.map(async (contact: any) => {
                const opp = await this.opportunityModel.findOne({ contactId: contact._id }, { _id: 1 });
                // if (opp.length > 0) console.log(opp.length, contact._id);
                // return opp.length;
                await this.leadModel.updateOne(
                    { _id: contact._id, contactId: { $exists: false } },
                    {
                        $set: {
                            contactId: contact._id,
                            status: opp?._id && contact.status === "active" ? "converted" : contact.status,
                            deleted: contact.deleted,
                            csrId: contact.csrId,
                            ...(contact?.stageId ? { stageId: contact.stageId } : {}),
                            ...(opp?._id ? { oppId: opp._id } : {}),
                            ...(contact?.newLeadDate ? { newLeadDate: contact.newLeadDate } : {}),
                        },
                    },
                );
            });
            console.log(updatePromises.length, "updatePromises");

            const leads3 = await this.leadModel.find({
                contactId: { $exists: false },
                phone: { $exists: true },
            });
            const contacts3 = await this.contactModel.find({
                phone: { $in: leads3.map((lead) => lead.phone) },
                stageId: { $exists: true },
            });
            console.log(leads3.length, contacts3.length);
            const updatePromises3 = contacts3.map(async (contact: any) => {
                const opp = await this.opportunityModel.findOne({ contactId: contact._id }, { _id: 1 });
                // if (opp.length > 0) console.log(opp.length, contact._id);
                // return opp.length;
                await this.leadModel.updateOne(
                    { phone: contact.phone, contactId: { $exists: false } },
                    {
                        $set: {
                            contactId: contact._id,
                            status: opp?._id && contact.status === "active" ? "converted" : contact.status,
                            deleted: contact.deleted,
                            csrId: contact.csrId,
                            ...(contact?.stageId ? { stageId: contact.stageId } : {}),
                            ...(opp?._id ? { oppId: opp._id } : {}),
                            ...(contact?.newLeadDate ? { newLeadDate: contact.newLeadDate } : {}),
                        },
                    },
                );
            });
            console.log(updatePromises3.length, "updatePromises3");

            const leads2 = await this.leadModel.find({
                contactId: { $exists: false },
                email: { $exists: true },
            });
            const contacts2 = await this.contactModel.find({
                email: { $in: leads2.map((lead) => lead.email) },
                stageId: { $exists: true },
            });
            console.log(leads2.length, contacts2.length);

            // create activity log for each opportunity
            const updatePromises2 = contacts2.map(async (contact: any) => {
                const opp = await this.opportunityModel.findOne({ contactId: contact._id }, { _id: 1 });
                // if (opp.length > 0) console.log(opp.length, contact._id);
                // return opp.length;
                await this.leadModel.updateOne(
                    { email: contact.email, contactId: { $exists: false } },
                    {
                        $set: {
                            contactId: contact._id,
                            status: opp?._id && contact.status === "active" ? "converted" : contact.status,
                            deleted: contact.deleted,
                            csrId: contact.csrId,
                            ...(contact?.stageId ? { stageId: contact.stageId } : {}),
                            ...(opp?._id ? { oppId: opp._id } : {}),
                            ...(contact?.newLeadDate ? { newLeadDate: contact.newLeadDate } : {}),
                        },
                    },
                );
            });
            console.log(updatePromises2.length, "updatePromises2");

            // const leads4 = await this.leadModel.find({
            //     contactId: { $exists: false },
            //     phone: { $exists: true },
            // });
            // const contacts4 = await this.contactModel.find({
            //     phone: { $in: leads4.map((lead) => lead.phone) },
            // });
            // // remove duplicates from contacts4
            // const uniqueContacts4 = contacts4.filter(
            //     (contact, index, self) => index === self.findIndex((c) => c.phone === contact.phone),
            // );
            // console.log(leads4.length, contacts4.length, uniqueContacts4.length);
            // const updatePromises4 = uniqueContacts4.map((contact: any) => {
            //     if (contact.stageId) {
            //         return this.leadModel.updateOne(
            //             { phone: contact.phone, contactId: { $exists: false } },
            //             {
            //                 $set: { contactId: contact._id },
            //             },
            //         );
            //     }
            // });

            // await Promise.all(updatePromises4);

            // const leads5 = await this.leadModel.find({
            //     contactId: { $exists: false },
            //     email: { $exists: true },
            // });
            // const contacts5 = await this.contactModel.find({
            //     email: { $in: leads5.map((lead) => lead.email) },
            // });
            // console.log(leads5.length, contacts5.length);
            // const updatePromises5 = contacts5.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { email: contact.email, contactId: { $exists: false } },
            //         {
            //             $set: { contactId: contact._id },
            //         },
            //     );
            // });

            // await Promise.all(updatePromises5);

            console.log("Successfully migrated lead to contact");
        } catch (error) {
            console.error("Lead to contact migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate lead to contact");
        }
    }

    // check all contact which have newLeadDate and have stageId then check if it has a lead for that contact
    async checkLeadForContact() {
        try {
            console.log("Starting lead check for contact");

            const contacts = await this.contactModel.find({
                newLeadDate: { $exists: true },
                stageId: { $exists: true },
            });
            console.log(contacts.length);

            // check if contact has a lead
            const updatePromises = contacts.map(async (contact: any) => {
                const lead = await this.leadModel.findOne({ contactId: contact._id });
                if (!lead) {
                    console.log("No lead found for contact", contact._id);
                    // create lead
                    const l = await this.leadModel.create({
                        contactId: contact._id,
                        stageId: contact.stageId,
                        status: contact.status,
                        deleted: contact.deleted,
                        csrId: contact.csrId,
                        newLeadDate: contact.newLeadDate,
                        tracking: contact?.tracking[0] || {},
                        createdBy: contact.createdBy,
                        createdAt: contact.createdAt,
                        zapierLead: contact.zapierLead,
                        workType: contact.workType,
                        referredBy: contact?.referredBy,
                    });
                    console.log("Created lead for contact", l._id);
                }
            });

            console.log("Successfully migrated lead stage");
        } catch (error) {
            console.error("Lead stage migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate lead stage");
        }
    }

    // script for duplicate contacts
    // async migrateDuplicateContacts() {
    //     try {
    //         console.log("Starting duplicate contact migration");

    //         const duplicatePhones = await this.contactModel.aggregate([
    //             {
    //                 $group: {
    //                     _id: "$phone",
    //                     count: { $sum: 1 },
    //                     docs: {
    //                         $push: {
    //                             _id: "$_id",
    //                             fullName: "$fullName",
    //                             phone: "$phone",
    //                             email: "$email",
    //                             stageId: "$stageId",
    //                         },
    //                     },
    //                 },
    //             },
    //             {
    //                 $match: {
    //                     count: { $gt: 1 },
    //                 },
    //             },
    //             {
    //                 $unwind: "$docs",
    //             },
    //             {
    //                 $replaceRoot: {
    //                     newRoot: "$docs",
    //                 },
    //             },
    //             {
    //                 $match: {
    //                     stageId: { $exists: true },
    //                     phone: { $exists: true },
    //                 },
    //             },
    //             {
    //                 $project: {
    //                     _id: 1,
    //                     fullName: 1,
    //                     phone: 1,
    //                     email: 1,
    //                     stageId: 1,
    //                 },
    //             },
    //         ]);
    //         console.log(`Found ${duplicatePhones.length} phone numbers with duplicates`);

    //         let totalUpdated = 0;

    //         // Process each group of duplicates
    //         for (const phoneGroup of duplicatePhones) {
    //             // Sort by updatedAt descending to get the most recent first
    //             const sortedDocs = phoneGroup.docs.sort((a, b) => b.updatedAt - a.updatedAt);

    //             // Keep the first one (most recent), mark others as deleted
    //             const idsToDelete = sortedDocs.slice(1).map((doc) => doc._id);

    //             if (idsToDelete.length > 0) {
    //                 const result = await this.contactModel.updateMany(
    //                     { _id: { $in: idsToDelete } },
    //                     {
    //                         $set: {
    //                             deleted: true,
    //                             deletedAt: new Date(),
    //                             deletedReason: "Duplicate phone number - older record",
    //                         },
    //                     },
    //                 );

    //                 totalUpdated += result.modifiedCount;
    //                 console.log(
    //                     `Phone: ${phoneGroup._id} - Marked ${result.modifiedCount} older records as deleted`,
    //                 );
    //             }
    //         }

    //         console.log(`Total records marked as deleted: ${totalUpdated}`);
    //     } catch (error) {
    //         console.error("Duplicate contact migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate duplicate contacts");
    //     }
    // }

    // script to migrate stage name to stageId on lead schema
    async migrateLeadStage() {
        try {
            console.log("Starting lead stage migration");

            await this.leadModel.updateMany({ stage: { $exists: true } }, { $rename: { stage: "stageId" } });

            console.log("Successfully migrated lead stage");

            // find all leads where stageId is not present then add stageId from contact by matching _id of contact and _id of lead
            // const contacts = await this.contactModel.find({
            //     stageId: { $exists: true },
            // });
            // console.log(contacts.length);
            // const updatePromises = contacts.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { stageId: contact.stageId, status: contact.status },
            //         },
            //     );
            // });
            // await Promise.all(updatePromises);

            // await this.leadModel.updateMany({ lost: true }, { $set: { status: "lost" } });

            // const leads = await this.leadModel.find({});
            // const contacts2 = await this.contactModel.find({
            //     _id: { $in: leads.map((lead) => lead.contactId) },
            //     stageId: { $exists: true },
            // });
            // console.log(leads.length, contacts2.length);
            // const updatePromises2 = contacts2.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { stageId: contact.stageId },
            //         },
            //     );
            // });

            // await Promise.all(updatePromises2);

            // const contacts3 = await this.contactModel.find({
            //     _id: { $in: leads.map((lead) => lead.contactId) },
            // });
            // console.log(leads.length, contacts3.length);
            // const updatePromises3 = contacts3.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { status: contact.status },
            //         },
            //     );
            // });
            // await Promise.all(updatePromises3);

            // only lead type contact
            // const contacts4 = await this.contactModel.find({
            //     type: ContactTypeEnum.LEAD,
            // });
            // const leads4 = await this.leadModel.find({
            //     contactId: { $in: contacts4.map((con) => con._id) },
            // });
            // console.log(contacts4.length, leads4.length, "Lead Type");
            // const updatePromises4 = contacts4.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { status: contact.status, stageId: contact.stageId, deleted: contact.deleted },
            //         },
            //     );
            // });
            // console.log(updatePromises4.length, "updatePromises4");
            // await Promise.all(updatePromises4);

            console.log("Successfully migrated lead stage");
        } catch (error) {
            console.error("Lead stage migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate lead stage");
        }
    }

    // Script to fix incorrect contactId assignments after migrateLeadToContact2
    async fixIncorrectContactIdAssignments() {
        try {
            console.log("Starting fix for incorrect contactId assignments");

            let fixedCount = 0;
            let noPhoneNoEmailCount = 0;
            let noMatchFoundCount = 0;
            let alreadyCorrectCount = 0;
            let wrongContactPhone = 0;
            let wrongContactEmail = 0;
            let oppCount = 0;
            const noMatchFound = [];
            const noPhoneNoEmail = [];

            // Get all leads where _id is not matching with contactId (indicating potential wrong assignment)
            const leadsWithMismatchedContactId = await this.leadModel
                .find({
                    contactId: { $exists: true },
                    // $expr: { $ne: ["$_id", "$contactId"] },
                })
                .select("_id contactId phone email companyId firstName lastName");

            console.log(`Found ${leadsWithMismatchedContactId.length} leads with mismatched contactId`);

            for (const leadData of leadsWithMismatchedContactId) {
                const lead: any = leadData;
                // console.log(`\n--- Processing Lead ID: ${lead._id} ---`);
                // console.log(`Current contactId: ${lead.contactId}`);
                // console.log(`Lead phone: ${lead.phone || "N/A"}`);
                // console.log(`Lead email: ${lead.email || "N/A"}`);

                let correctContact = null;
                let matchType = "";

                // First try to match by phone if lead has phone
                if (lead.phone) {
                    correctContact = await this.contactModel
                        .findOne({
                            phone: lead.phone,
                            companyId: lead.companyId,
                            // deleted: { $ne: true },
                        })
                        .select("_id phone email fullName");

                    if (correctContact) {
                        matchType = "phone";
                        console.log(
                            `Found contact by phone: ${correctContact._id} (${correctContact.fullName})`,
                        );
                    }
                }

                // If no phone match and lead has email, try to match by email
                if (!correctContact && lead.email) {
                    correctContact = await this.contactModel
                        .findOne({
                            email: lead.email,
                            companyId: lead.companyId,
                            // deleted: { $ne: true },
                        })
                        .select("_id phone email fullName");

                    if (correctContact) {
                        matchType = "email";
                        console.log(
                            `Found contact by email: ${correctContact._id} (${correctContact.fullName})`,
                        );
                    }
                }

                // Check if the current contactId is already correct
                if (correctContact && correctContact._id === lead.contactId) {
                    // console.log(`✓ ContactId is already correct for lead ${lead._id}`);
                    alreadyCorrectCount++;
                    continue;
                }

                // If we found a correct contact and it's different from current contactId
                if (correctContact && correctContact._id !== lead.contactId) {
                    if (matchType === "phone") {
                        wrongContactPhone++;
                    } else if (matchType === "email") {
                        wrongContactEmail++;
                    }
                    // console.log(
                    //     `🔄 Need to update contactId from ${lead.contactId} to ${correctContact._id} (matched by ${matchType})`,
                    // );

                    // Get the opportunity for this correct contact
                    const opps = await this.opportunityModel.find(
                        {
                            contactId: correctContact._id,
                        },
                        { _id: 1, newLeadDate: 1, street: 1 },
                    );
                    if (opps.length > 0) {
                        oppCount++;
                    }

                    const opp = opps.length > 1 ? opps.find((opp) => opp.street === lead.street) : opps[0];

                    // Get the correct contact's full data for other fields
                    const fullCorrectContact = await this.contactModel.findById(correctContact._id);

                    // Update the lead with correct contactId and related fields
                    const updateData = {
                        contactId: correctContact._id,
                        ...(fullCorrectContact?.status && {
                            status:
                                opp?._id && fullCorrectContact.status === "active"
                                    ? "converted"
                                    : fullCorrectContact.status,
                        }),
                        ...(fullCorrectContact?.deleted !== undefined && {
                            deleted: fullCorrectContact.deleted,
                        }),
                        ...(fullCorrectContact?.csrId && {
                            csrId: fullCorrectContact.csrId,
                        }),
                        ...(fullCorrectContact?.stageId && { stageId: fullCorrectContact.stageId }),
                        ...(opp?._id && { oppId: opp._id }),
                        ...(fullCorrectContact?.newLeadDate && {
                            newLeadDate: fullCorrectContact.newLeadDate,
                        }),
                    };

                    // console.log(`Update data:`, updateData);

                    // COMMENTED OUT FOR REVIEW - UNCOMMENT TO ACTUALLY UPDATE
                    // /*
                    await this.leadModel.updateOne({ _id: lead._id }, { $set: updateData });
                    // */

                    console.log(
                        `✅ Would update lead ${lead._id} with correct contactId ${correctContact._id} opp length ${opps.length}`,
                    );
                    fixedCount++;
                } else if (!correctContact) {
                    if (!lead.phone && !lead.email) {
                        console.log(`❌ Lead ${lead._id} has no phone or email to match with`);
                        noPhoneNoEmailCount++;
                        noPhoneNoEmail.push(lead._id);
                    } else {
                        console.log(`❌ No matching contact found for lead ${lead._id}`);
                        noMatchFoundCount++;
                        noMatchFound.push(lead._id);
                    }
                }
            }

            console.log("\n=== SUMMARY ===");
            console.log(`Total leads processed: ${leadsWithMismatchedContactId.length}`);
            console.log(`Wrong leads with phone number: ${wrongContactPhone}`);
            console.log(`Wrong leads with email: ${wrongContactEmail}`);
            console.log(`Leads that would be fixed: ${fixedCount}`);
            console.log(`Leads already correct: ${alreadyCorrectCount}`);
            console.log(`Leads with no phone/email: ${noPhoneNoEmailCount}`);
            console.log(`Leads with no matching contact: ${noMatchFoundCount}`);
            console.log(`Leads with opportunity: ${oppCount}`);
            console.log(noPhoneNoEmail);
            console.log(noMatchFound);
            // console.log("\n⚠️  THIS WAS A DRY RUN - NO ACTUAL UPDATES WERE MADE");
            // console.log("⚠️  UNCOMMENT THE UPDATE QUERY IN THE CODE TO APPLY CHANGES");
        } catch (error) {
            console.error("Fix incorrect contactId assignments failed:", error);
            throw new InternalServerErrorException("Failed to fix incorrect contactId assignments");
        }
    }

    // write code to find all leads where newLeadDate is not in 12hr to 24hr range of createdAt
    async fixNewLeadDate() {
        try {
            const leads: any = await this.leadModel
                .find({
                    newLeadDate: { $exists: true },
                })
                .populate("contactId", "newLeadDate dateReceived", "Contact");
            console.log("Starting fix for newLeadDate");

            let count = 0;
            for (const lead of leads) {
                const newLeadDate = new Date(lead.newLeadDate);
                const createdAt = new Date(lead.createdAt);
                const dateReceived = new Date(lead?.contactId?.dateReceived);
                const newLeadDate2 = new Date(lead?.contactId?.newLeadDate);

                const timeDifference = Math.abs(newLeadDate.getTime() - createdAt.getTime());
                const timeDifference2 = Math.abs(newLeadDate.getTime() - newLeadDate2.getTime());
                const timeDifference3 = Math.abs(newLeadDate.getTime() - dateReceived.getTime());
                const hoursDifference = timeDifference / (1000 * 60 * 60);
                const hoursDifference2 = timeDifference2 / (1000 * 60 * 60);
                const hoursDifference3 = timeDifference3 / (1000 * 60 * 60);

                if (hoursDifference > 24) {
                    count++;
                    // console.log(`${count} Lead ${lead?._id}  - ${hoursDifference} hours difference`);

                    // we need to find the correct newLeadDate
                    if (hoursDifference2 < 24 && hoursDifference2 > 0) {
                        console.log(
                            `${count} Lead ${lead?._id}  - ${hoursDifference} hours difference - ${hoursDifference2} hours difference with contact`,
                        );
                    }
                    if (hoursDifference3 < 24 && hoursDifference3 > 0) {
                        console.log(
                            `${count} Lead ${lead?._id}  - ${hoursDifference} hours difference - ${hoursDifference3} hours difference with dateReceived`,
                        );
                    }

                    if (lead?.oppId) {
                        const opp = await this.opportunityModel.findOne(
                            { _id: lead?.oppId },
                            { contactId: 1, newLeadDate: 1 },
                        );
                        if (opp) {
                            const newLeadDate3 = new Date(opp.newLeadDate);
                            const timeDifference4 = Math.abs(newLeadDate.getTime() - newLeadDate3.getTime());
                            const hoursDifference4 = timeDifference4 / (1000 * 60 * 60);
                            if (hoursDifference4 > 24) {
                                console.log(
                                    `${count} Lead ${lead?._id}  - ${hoursDifference} hours difference - ${hoursDifference4} hours difference with opportunity`,
                                );
                            }
                        }
                    }
                }
            }

            console.log(`Found ${leads.length} leads with newLeadDate`);
        } catch (error) {
            console.error("Fix newLeadDate failed:", error);
            throw new InternalServerErrorException("Failed to fix newLeadDate");
        }
    }

    /**
     * Migration function to update contact types based on their opportunities
     *
     * Logic:
     * - If contact has opportunity but no saleDate or orderId → type: "prospect"
     * - If contact has opportunity with both saleDate and orderId → type: "client"
     * - If contact has no opportunity → don't update type
     *
     * @param dryRun - If true, only logs what would be changed without making updates
     */
    async migrateContactTypesByOpportunity(dryRun = true) {
        try {
            console.log("Starting contact type migration based on opportunities");
            console.log(`DRY RUN MODE: ${dryRun ? "ON" : "OFF"}`);

            let prospectCount = 0;
            let clientCount = 0;
            let noOpportunityCount = 0;
            let alreadyCorrectCount = 0;
            let errorCount = 0;

            // Get all all contacts & opportunities
            const [contacts, opps] = await Promise.all([
                this.contactModel
                    .find({
                        // deleted: { $ne: true },
                    })
                    .select("_id fullName firstName lastName type companyId"),
                this.opportunityModel
                    .find({
                        // deleted: { $ne: true },
                    })
                    .select("_id contactId saleDate orderId"),
            ]);

            console.log(`Found ${contacts.length} contacts to process`);

            for (const contact of contacts) {
                try {
                    console.log(
                        `\n--- Processing Contact: ${contact.fullName || contact.firstName} (${
                            contact._id
                        }) ---`,
                    );
                    console.log(`Current type: ${contact.type}`);

                    // Find opportunities for this contact
                    const opportunities = opps.filter(
                        (opp) => opp.contactId.toString() === contact._id.toString(),
                    );

                    console.log(`Found ${opportunities.length} opportunities for this contact`);

                    if (opportunities.length === 0) {
                        console.log(`❌ No opportunities found - skipping type update`);
                        noOpportunityCount++;
                        continue;
                    }

                    // Determine the new type based on opportunities
                    let newType = null;
                    let hasClientOpportunity = false;

                    for (const opp of opportunities) {
                        console.log(
                            `  Opportunity ${opp._id}: saleDate=${opp.saleDate ? "YES" : "NO"}, orderId=${
                                opp.orderId ? "YES" : "NO"
                            }`,
                        );

                        // If any opportunity has both saleDate and orderId, contact should be client
                        if (opp.saleDate && opp.orderId) {
                            hasClientOpportunity = true;
                            break;
                        }
                    }

                    if (hasClientOpportunity) {
                        newType = ContactTypeEnum.CLIENT;
                        console.log(`🔄 Should be CLIENT (has opportunity with saleDate and orderId)`);
                    } else {
                        newType = ContactTypeEnum.PROSPECT;
                        console.log(`🔄 Should be PROSPECT (has opportunity but no saleDate/orderId)`);
                    }

                    // Check if type is already correct
                    if (contact.type === newType) {
                        console.log(`✓ Type is already correct (${newType})`);
                        alreadyCorrectCount++;
                        continue;
                    }

                    console.log(`🔄 Need to update type from "${contact.type}" to "${newType}"`);

                    if (!dryRun) {
                        await this.contactModel.updateOne({ _id: contact._id }, { $set: { type: newType } });
                        console.log(`✅ Updated contact ${contact._id} type to ${newType}`);
                    } else {
                        console.log(`✅ Would update contact ${contact._id} type to ${newType}`);
                    }

                    if (newType === ContactTypeEnum.PROSPECT) {
                        prospectCount++;
                    } else if (newType === ContactTypeEnum.CLIENT) {
                        clientCount++;
                    }
                } catch (contactError) {
                    console.error(`❌ Error processing contact ${contact._id}:`, contactError.message);
                    errorCount++;
                }
            }

            console.log("\n=== MIGRATION SUMMARY ===");
            console.log(`Total contacts processed: ${contacts.length}`);
            console.log(`Contacts ${dryRun ? "that would be" : ""} updated to PROSPECT: ${prospectCount}`);
            console.log(`Contacts ${dryRun ? "that would be" : ""} updated to CLIENT: ${clientCount}`);
            console.log(`Contacts already correct: ${alreadyCorrectCount}`);
            console.log(`Contacts with no opportunities (skipped): ${noOpportunityCount}`);
            console.log(`Contacts with errors: ${errorCount}`);

            if (dryRun) {
                console.log("\n⚠️  THIS WAS A DRY RUN - NO ACTUAL UPDATES WERE MADE");
                console.log("⚠️  Call migrateContactTypesByOpportunity(false) TO APPLY CHANGES");
            } else {
                console.log("\n✅ MIGRATION COMPLETED - UPDATES HAVE BEEN APPLIED TO THE DATABASE");
            }

            return new OkResponse({
                message: "Contact type migration completed",
                summary: {
                    totalProcessed: contacts.length,
                    prospectUpdates: prospectCount,
                    clientUpdates: clientCount,
                    alreadyCorrect: alreadyCorrectCount,
                    noOpportunities: noOpportunityCount,
                    errors: errorCount,
                    dryRun,
                },
            });
        } catch (error) {
            console.error("Contact type migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate contact types");
        }
    }
    // Migration: Migrate all referrers into contacts, and update all opportunities' referredBy fields to point to the new/existing contact._id.
    async migrateReferrersToContactsAndUpdateOpportunities(companyId: string) {
        try {
            console.log("Starting migration of referrers to contacts and updating opportunities");
            const referrers = await this.referrersModel.find({ companyId });
            const refToContactMap = new Map();
            let matched = 0;
            let created = 0;
            for (const ref of referrers) {
                const name = ref.name.trim();
                let contact = await this.contactModel.findOne({
                    companyId,
                    fullName: { $regex: `^${name}$`, $options: "i" },
                });
                if (contact) {
                    matched++;
                    console.log(
                        `Matched referrer '${name}' to contact '${contact.fullName}' (${contact._id})`,
                    );
                } else {
                    const nameParts = name.split(" ");
                    if (nameParts.length === 2) {
                        const firstName = nameParts[0];
                        const lastName = nameParts[1];
                        contact = await this.contactModel.create({
                            fullName: name,
                            firstName,
                            lastName,
                            isBusiness: false,
                            companyId,
                            type: ContactTypeEnum.REFERRER,
                            dateReceived: ref.createdAt || new Date(),
                            createdAt: ref.createdAt,
                            updatedAt: ref.updatedAt,
                        });
                        console.log(`Created new contact for referrer '${name}' (${contact._id})`);
                    } else if (nameParts.length > 2) {
                        const firstName = nameParts[0];
                        const lastName = "";
                        contact = await this.contactModel.create({
                            fullName: name,
                            firstName,
                            lastName,
                            isBusiness: false,
                            companyId,
                            type: ContactTypeEnum.REFERRER,
                            dateReceived: ref.createdAt || new Date(),
                            createdAt: ref.createdAt,
                            updatedAt: ref.updatedAt,
                        });
                        console.log(`Created new contact for referrer '${name}' (${contact._id})`);
                    } else {
                        const firstName = nameParts[0];
                        const lastName = "";
                        contact = await this.contactModel.create({
                            fullName: name,
                            firstName,
                            lastName,
                            isBusiness: false,
                            companyId,
                            type: ContactTypeEnum.REFERRER,
                            dateReceived: ref.createdAt || new Date(),
                            createdAt: ref.createdAt,
                            updatedAt: ref.updatedAt,
                        });
                        console.log(`Created new contact for referrer '${name}' (${contact._id})`);
                    }
                    created++;
                }
                refToContactMap.set(ref._id.toString(), contact._id.toString());
            }
            // Update all opportunities and contacts whose referredBy points to a referrer
            let totalOppsUpdated = 0;
            let totalContactsUpdated = 0;
            for (const [refId, contactId] of refToContactMap.entries()) {
                // Update opportunities
                const oppUpdateRes = await this.opportunityModel.updateMany(
                    { companyId, referredBy: refId },
                    { $set: { referredBy: contactId } },
                );
                if (oppUpdateRes.modifiedCount > 0) {
                    console.log(
                        `Updated ${oppUpdateRes.modifiedCount} opportunities referredBy ${refId} -> ${contactId}`,
                    );
                    totalOppsUpdated += oppUpdateRes.modifiedCount;
                }

                // Update contacts
                const contactUpdateRes = await this.contactModel.updateMany(
                    { companyId, referredBy: refId },
                    { $set: { referredBy: contactId } },
                );
                if (contactUpdateRes.modifiedCount > 0) {
                    console.log(
                        `Updated ${contactUpdateRes.modifiedCount} contacts referredBy ${refId} -> ${contactId}`,
                    );
                    totalContactsUpdated += contactUpdateRes.modifiedCount;
                }
            }
            console.log(
                `Migration complete. Matched: ${matched}, Created: ${created}, Opportunities updated: ${totalOppsUpdated}, Contacts updated: ${totalContactsUpdated}`,
            );
        } catch (error) {
            console.error("Migration failed:", error);
            throw new InternalServerErrorException(
                "Failed to migrate referrers to contacts and update opportunities",
            );
        }
    }

    /**
     * Get all opportunities where referredBy matches the given contactId.
     * Returns: PO, num, fullName, nextAction, stageGroup (from stage), status, newLeadDate
     */
    async getOpportunitiesReferredByContact(contactId: string, user) {
        try {
            const pipeline = [
                { $match: { companyId: user.companyId, referredBy: contactId, deleted: false } },
                {
                    $lookup: {
                        from: "CrmStage",
                        localField: "stage",
                        foreignField: "_id",
                        as: "stageData",
                        pipeline: [{ $project: { stageGroup: 1, name: 1 } }],
                    },
                },
                { $unwind: { path: "$stageData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "Contact",
                        localField: "contactId",
                        foreignField: "_id",
                        as: "referredByContact",
                        pipeline: [{ $project: { fullName: 1 } }],
                    },
                },
                { $unwind: { path: "$referredByContact", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        _id: 1,
                        PO: 1,
                        num: 1,
                        fullName: "$referredByContact.fullName",
                        stageGroup: "$stageData.stageGroup",
                        stageName: "$stageData.name",
                        status: 1,
                        newLeadDate: 1,
                    },
                },
            ];
            const opportunities = await this.opportunityModel.aggregate(pipeline);
            return new OkResponse({ opps: opportunities });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Logs the number of referrers matching existing contacts and the number that would require new contact creation, without modifying the database.
     * Does not perform any writes.
     */
    async logReferrerContactMatchStats(companyId: string) {
        try {
            console.log("Starting dry-run log for referrer to contact migration");
            const referrers = await this.referrersModel.find({ companyId });
            let matched = 0;
            let wouldCreate = 0;
            for (const ref of referrers) {
                const name = ref.name.trim();
                const contact = await this.contactModel.findOne({
                    companyId,
                    fullName: name,
                });
                if (contact) {
                    matched++;
                    // Optionally log details:
                    // console.log(`Would match referrer '${name}' to contact '${contact.fullName}' (${contact._id})`);
                } else {
                    wouldCreate++;
                    // Optionally log details:
                    // console.log(`Would create new contact for referrer '${name}'`);
                }
            }
            console.log(`Dry-run summary: Matched referrers to existing contacts: ${matched}`);
            console.log(`Dry-run summary: Would create new contacts for referrers: ${wouldCreate}`);
            console.log(`Total referrers processed: ${referrers.length}`);
        } catch (error) {
            console.error("Dry-run log for referrer to contact migration failed:", error);
            throw new InternalServerErrorException("Failed to log referrer/contact match stats");
        }
    }

    async getOpenOpportunitiesAndActiveLeads(companyId: string, contactId: string) {
        if (!contactId) {
            throw new BadRequestException("contactId is required");
        }
        try {
            const oppMatch: any = {
                companyId,
                deleted: false,
                status: "active",
                saleDate: { $exists: false },
                orderId: { $exists: false },
                contactId,
            };
            const opps = await this.opportunityModel.aggregate([
                { $match: oppMatch },
                {
                    $lookup: {
                        from: "CrmStage",
                        localField: "stage",
                        foreignField: "_id",
                        as: "stageData",
                        pipeline: [{ $project: { stageGroup: 1 } }],
                    },
                },
                { $unwind: { path: "$stageData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "ProjectType",
                        localField: "oppType",
                        foreignField: "_id",
                        as: "oppTypeData",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                { $unwind: { path: "$oppTypeData", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        _id: 1,
                        PO: 1,
                        num: 1,
                        stage: { stageGroup: "$stageData.stageGroup" },
                        oppType: "$oppTypeData",
                    },
                },
            ]);

            const leadQuery: any = {
                companyId: companyId,
                status: "active",
                deleted: false,
                contactId: contactId,
            };
            const leads = await this.leadModel
                .findOne(leadQuery, {
                    _id: 1,
                    contactId: 1,
                    leadSourceId: 1,
                    campaignId: 1,
                    oppId: 1,
                    csrId: 1,
                    workType: 1,
                    newLeadDate: 1,
                    referredBy: 1,
                })
                .populate([
                    {
                        path: "leadSourceId",
                        select: "name _id",
                        model: "LeadSource",
                    },
                    {
                        path: "campaignId",
                        select: "name _id",
                        model: "Campaign",
                    },
                ]);

            // Transform to include the name fields
            const transformedLeads = leads
                ? {
                      _id: leads._id,
                      contactId: leads.contactId,
                      leadSourceId: (leads.leadSourceId as any)?._id,
                      campaignId: (leads.campaignId as any)?._id,
                      oppId: leads.oppId,
                      csrId: leads.csrId,
                      workType: leads.workType,
                      newLeadDate: leads.newLeadDate,
                      leadSourceName: (leads.leadSourceId as any)?.name,
                      campaignName: (leads.campaignId as any)?.name,
                      referredBy: leads?.referredBy,
                  }
                : null;

            return new OkResponse({ opps, leads: transformedLeads });
        } catch (error) {
            throw error instanceof HttpException ? error : new InternalServerErrorException(error.message);
        }
    }

    /**
     * Migration function to convert invalidLeadReason from string to object structure
     *
     * Old format: string value like "Current Client", "Spam", etc.
     * New format: { reason: string, notes: string }
     *
     * Mapping:
     * - Known values: 'Current Client', 'Looking For Work', 'Outside Service Area',
     *   'Purchase Material Only', 'Service Not Provided', 'Spam', 'Unreachable',
     *   'Vendor', 'Warranty Call' → reason field
     * - Unknown values → reason: 'Other (Describe in notes)', notes: original value
     */
    async migrateInvalidLeadReasonToObjectStructure() {
        try {
            console.log("Starting migration of invalidLeadReason from string to object structure");

            const knownReasons = [
                "current client",
                "looking for work",
                "outside service area",
                "purchase material only",
                "service not provided",
                "spam",
                "unreachable",
                "vendor",
                "warranty call",
            ];

            let leadsMigrated = 0;

            // Migrate leads
            console.log("Migrating leads...");
            const leadsWithStringReason = await this.leadModel.find({
                status: "invalid",
            });

            console.log(`Found ${leadsWithStringReason.length} leads with string invalidLeadReason`);

            for (const lead of leadsWithStringReason) {
                const oldReason = lead.invalidLeadReason as unknown as string;
                const normalizedReason = oldReason?.trim()?.toLowerCase();

                let newReasonObj: { reason: string; notes: string };
                if (knownReasons.includes(normalizedReason)) {
                    newReasonObj = {
                        reason: oldReason?.trim(),
                        notes: "",
                    };
                } else {
                    newReasonObj = {
                        reason: "Other (Describe in notes)",
                        notes: oldReason?.trim() || "",
                    };
                }

                await this.leadModel.updateOne(
                    { _id: lead._id },
                    { $set: { invalidLeadReason: newReasonObj } },
                );

                leadsMigrated++;
                console.log(`Migrated lead ${lead._id}: "${oldReason}" → ${JSON.stringify(newReasonObj)}`);
            }

            // migrate leads for lostReson
            let lostLeadsMigrated = 0;
            const leadsWithLostReason = await this.leadModel.find({
                status: "lost",
            });

            console.log(`Found ${leadsWithLostReason.length} leads with lostReason`);

            const knownLostReason = [
                "too expensive",
                "price shopping",
                "went with other provider",
                "discuss with partner",
                "wants to wait",
                "ghosted",
            ];

            for (const lead of leadsWithLostReason) {
                const oldReason = lead.lostReason as unknown as string;
                const normalizedReason = oldReason?.trim()?.toLowerCase();

                let newReasonObj: { reason: string; notes: string };
                if (knownLostReason.includes(normalizedReason)) {
                    newReasonObj = {
                        reason: oldReason?.trim(),
                        notes: "",
                    };
                } else {
                    newReasonObj = {
                        reason: "Other (Describe in notes)",
                        notes: oldReason?.trim() || "",
                    };
                }

                await this.leadModel.updateOne({ _id: lead._id }, { $set: { lostReason: newReasonObj } });

                lostLeadsMigrated++;
                console.log(`Migrated lead ${lead._id}: "${oldReason}" → ${JSON.stringify(newReasonObj)}`);
            }

            // Migrate contacts
            console.log("\n=== MIGRATION SUMMARY ===");
            console.log(`Leads migrated: ${leadsMigrated}`);
            console.log(`Lost leads migrated: ${lostLeadsMigrated}`);
            console.log(`Completed migration of invalidLeadReason from string to object structure`);
        } catch (error) {
            console.error("InvalidLeadReason migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate invalidLeadReason structure");
        }
    }

    /**
     * Migrates phone numbers in the contact collection by removing non-numeric characters
     */
    async migratePhoneNumbers(dryRun = false) {
        console.log(`Starting phone number migration... Dry run: ${dryRun}`);

        try {
            // Find all contacts with phone numbers that contain non-numeric characters
            const contactsWithFormattedPhones = await this.contactModel.find({
                phone: {
                    $exists: true,
                    $ne: null,
                    $nin: ["", null],
                    $regex: /[^0-9]/, // Contains non-numeric characters
                },
            });

            console.log(`Found ${contactsWithFormattedPhones.length} contacts with formatted phone numbers`);

            const migrationStats = {
                totalContactsFound: contactsWithFormattedPhones.length,
                contactsProcessed: 0,
                contactsUpdated: 0,
                errors: 0,
                changes: [] as Array<{
                    contactId: string;
                    oldPhone: string;
                    newPhone: string;
                }>,
            };

            for (const contact of contactsWithFormattedPhones) {
                try {
                    const oldPhone = contact.phone;
                    // Remove all non-numeric characters
                    const newPhone = oldPhone.replace(/[^0-9]/g, "");

                    console.log(`Contact ${contact._id}: "${oldPhone}" -> "${newPhone}"`);

                    migrationStats.changes.push({
                        contactId: contact._id.toString(),
                        oldPhone,
                        newPhone,
                    });

                    if (!dryRun) {
                        // Update the contact with the new phone number
                        await this.contactModel.updateOne(
                            { _id: contact._id },
                            { $set: { phone: newPhone } },
                        );
                        migrationStats.contactsUpdated++;
                        console.log(`✓ Updated contact ${contact._id}`);
                    } else {
                        console.log(`[DRY RUN] Would update contact ${contact._id}`);
                    }

                    migrationStats.contactsProcessed++;
                } catch (error) {
                    console.error(`Error processing contact ${contact._id}:`, error);
                    migrationStats.errors++;
                }
            }

            console.log("\n=== Migration Summary ===");
            console.log(`Total contacts found: ${migrationStats.totalContactsFound}`);
            console.log(`Contacts processed: ${migrationStats.contactsProcessed}`);
            console.log(`Contacts updated: ${migrationStats.contactsUpdated}`);
            console.log(`Errors: ${migrationStats.errors}`);
            console.log(`Dry run: ${dryRun}`);

            if (dryRun) {
                console.log("\n=== Sample Changes (First 10) ===");
                migrationStats.changes.slice(0, 10).forEach((change) => {
                    console.log(`${change.contactId}: "${change.oldPhone}" -> "${change.newPhone}"`);
                });
                if (migrationStats.changes.length > 10) {
                    console.log(`... and ${migrationStats.changes.length - 10} more changes`);
                }
            }

            return migrationStats;
        } catch (error) {
            console.error("Error during phone number migration:", error);
            throw error instanceof HttpException ? error : new InternalServerErrorException(error.message);
        }
    }

    /**
     * Migration function to create Leads for Opportunities created after Jan 1, 2024
     * that don't have a corresponding Lead
     */
    async createLeadsForOppsMigration() {
        const dryRun = false; // Set to false to enable actual DB writes
        let session;
        try {
            console.log("Starting migration: Create Leads for Opportunities created after Jan 1, 2024...");

            if (!dryRun) {
                session = await this.connection.startSession();
                session.startTransaction();
            }

            // Find all opportunities created after Jan 1, 2024
            const opportunities = await this.opportunityModel.find({
                createdAt: { $gte: new Date("2024-01-01T00:00:00.000Z") },
            });

            console.log(`Found ${opportunities.length} opportunities created after Jan 1, 2024`);

            // Pre-fetch all existing leads for these opportunities
            const oppIds = opportunities.map((opp) => opp._id);
            const existingLeads = await this.leadModel
                .find({ oppId: { $in: oppIds }, deleted: false })
                .select("oppId _id");
            const existingLeadMap = new Map(existingLeads.map((lead) => [lead.oppId.toString(), lead._id]));

            // Pre-fetch all needed stageData by companyId
            // const companyIds = [...new Set(opportunities.map((opp) => opp.companyId).filter(Boolean))];
            const allStages = await this.crmStageModel.find({
                _id: "ec0be60b-3f20-4e7e-8501-d54c738e34aa",
                deleted: false,
                stageGroup: StageGroupEnum.Leads,
                sequence: 1,
            });
            const newLeadStage = allStages[0];
            // const stageMap = new Map(allStages.map((stage) => [stage.companyId.toString(), stage]));

            let createdLeadsCount = 0;
            let skippedCount = 0;
            let missingStageCount = 0;
            const bulkLeads = [];
            const bulkOppUpdates = [];

            for (const opportunity of opportunities) {
                // Check if a lead already exists for this opportunity
                if (existingLeadMap.has(opportunity._id.toString())) {
                    skippedCount++;
                    continue;
                }

                // Use the first 'New Lead' stage found
                const stageData = newLeadStage;
                if (!stageData) {
                    missingStageCount++;
                    continue;
                }

                if (!dryRun) {
                    const newLeadId = randomUUID();
                    bulkLeads.push({
                        insertOne: {
                            document: {
                                _id: newLeadId,
                                companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
                                contactId: opportunity.contactId,
                                oppId: opportunity._id,
                                status: "converted",
                                createdBy: opportunity.createdBy,
                                deleted: false,
                                createdAt: opportunity.createdAt,
                                updatedAt: opportunity.createdAt,
                                ...(opportunity.newLeadDate && { newLeadDate: opportunity.newLeadDate }),
                                ...(opportunity.leadSourceId && { leadSourceId: opportunity.leadSourceId }),
                                ...(opportunity.campaignId && { campaignId: opportunity.campaignId }),
                                ...(opportunity.oppType && { workType: opportunity.oppType }),
                                ...(opportunity.referredBy && { referredBy: opportunity.referredBy }),
                                csrId: opportunity.csrId || stageData.defaultCsrId,
                                stageId: stageData._id,
                            },
                        },
                    });
                    bulkOppUpdates.push({
                        updateOne: {
                            filter: { _id: opportunity._id },
                            update: { $set: { leadId: newLeadId } },
                        },
                    });
                    createdLeadsCount++;
                } else {
                    createdLeadsCount++;
                }
            }

            if (!dryRun && bulkLeads.length > 0) {
                await this.leadModel.bulkWrite(bulkLeads, { session });
                await this.opportunityModel.bulkWrite(bulkOppUpdates, { session });
            }

            if (!dryRun && session) {
                await session.commitTransaction();
                session.endSession();
            }

            console.log("Migration completed successfully!");
            console.log(`Total opportunities processed: ${opportunities.length}`);
            console.log(`Leads created: ${dryRun ? createdLeadsCount : bulkLeads.length}`);
            console.log(`Opportunities skipped (already had leads): ${skippedCount}`);
            console.log(`Opportunities skipped (missing stageData): ${missingStageCount}`);

            return {
                success: true,
                totalOpportunities: opportunities.length,
                leadsCreated: dryRun ? createdLeadsCount : bulkLeads.length,
                opportunitiesSkipped: skippedCount,
                opportunitiesSkippedMissingStage: missingStageCount,
                dryRun,
            };
        } catch (error) {
            console.error("Migration failed:", error);
            if (session && !dryRun) {
                await session.abortTransaction();
                session.endSession();
            }
            throw error;
        }
    }

    /**
     * Migration method to populate count fields for all existing contacts
     * This should be run once after deploying the count field changes
     */
    async migrateContactCounts(): Promise<void> {
        try {
            console.log("Starting contact counts migration...");

            // const query = companyId ? { companyId, deleted: false } : { deleted: false };
            const contacts = await this.contactModel.find({}, { _id: 1 });

            console.log(`Found ${contacts.length} contacts to migrate`);

            let processed = 0;
            const batchSize = 100;

            for (let i = 0; i < contacts.length; i += batchSize) {
                const batch = contacts.slice(i, i + batchSize);
                const updatePromises = batch.map((contact) =>
                    this.updateContactCounts(contact._id, "0f33b070-a7f2-43f3-8d07-54fdfd4378e3"),
                );

                await Promise.all(updatePromises);
                processed += batch.length;

                console.log(`Processed ${processed}/${contacts.length} contacts`);

                // Add a small delay to avoid overwhelming the database
                if (i + batchSize < contacts.length) {
                    await new Promise((resolve) => setTimeout(resolve, 100));
                }
            }

            console.log("Contact counts migration completed successfully!");
        } catch (error) {
            console.error("Error during contact counts migration:", error);
            throw error;
        }
    }
}
