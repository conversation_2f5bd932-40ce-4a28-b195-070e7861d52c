import { PickType } from "@nestjs/swagger";
import { CreateOpportunityDto } from "./create-opportunity.dto";

export class TransferLeadToContactDto extends PickType(CreateOpportunityDto, [
    "originalContact",
    "contactId",
    "currentDate",
    "transferLeadId",
] as const) {}

// /**
//  * Creates a new opportunity with proper transaction handling and validation
//  * @param memberId - ID of the member creating the opportunity
//  * @param companyId - Company ID
//  * @param opportunityDto - Opportunity data transfer object
//  * @param warrantyType - Whether this is a warranty opportunity
//  * @returns Created opportunity response
//  */
// async createOpportunity(
//     memberId: string,
//     companyId: string,
//     opportunityDto: CreateWarrantyOpportunityDto,
//     warrantyType: boolean,
// ) {
//     // Input validation
//     this.validateCreateOpportunityInput(memberId, companyId, opportunityDto);

//     const session = await this.connection.startSession();

//     try {
//         session.startTransaction();

//         const cleanedDto = cleanCreateData(opportunityDto);
//         const opportunityId = randomUUID();

//         // Validate and fetch required data
//         const contact = await this.validateAndFetchContact(cleanedDto.contactId, companyId);
//         const { PO, num } = await this.generatePOAndNumber(
//             contact,
//             cleanedDto.street,
//             companyId,
//             warrantyType,
//         );

//         // Handle contact transfers and lead management
//         await this.handleContactTransfer(cleanedDto, memberId, companyId, session);
//         await this.handleLeadManagement(cleanedDto, opportunityId, memberId, companyId, session);

//         // Create opportunity and related records
//         await this.createOpportunityRecord(
//             opportunityId,
//             PO,
//             num,
//             cleanedDto,
//             companyId,
//             warrantyType,
//             session,
//         );

//         // Create activity records
//         await this.createOpportunityActivity(
//             opportunityId,
//             cleanedDto.createdBy,
//             companyId,
//             cleanedDto.currentDate,
//             session,
//         );

//         // Update contact information
//         await this.updateContactAfterOpportunityCreation(contact, cleanedDto, companyId, session);

//         // Update contact counts (async, non-blocking)
//         this.updateContactCounts(cleanedDto, companyId);

//         await session.commitTransaction();

//         return new CreatedResponse({
//             message: "Opportunity created successfully!",
//             oppId: opportunityId,
//         });
//     } catch (error: any) {
//         await session.abortTransaction();
//         this.handleCreateOpportunityError(error);
//     } finally {
//         await session.endSession();
//     }
// }

// /**
//  * Validates input parameters for opportunity creation
//  */
// private validateCreateOpportunityInput(
//     memberId: string,
//     companyId: string,
//     opportunityDto: CreateWarrantyOpportunityDto,
// ): void {
//     if (!memberId || typeof memberId !== "string") {
//         throw new BadRequestException("Valid member ID is required");
//     }

//     if (!companyId || typeof companyId !== "string") {
//         throw new BadRequestException("Valid company ID is required");
//     }

//     if (!opportunityDto) {
//         throw new BadRequestException("Opportunity data is required");
//     }

//     if (!opportunityDto.contactId) {
//         throw new BadRequestException("Contact ID is required");
//     }

//     if (!opportunityDto.oppType) {
//         throw new BadRequestException("Opportunity type is required");
//     }

//     if (!opportunityDto.oppDate) {
//         throw new BadRequestException("Opportunity date is required");
//     }

//     if (!opportunityDto.newLeadDate) {
//         throw new BadRequestException("New lead date is required");
//     }
// }

// /**
//  * Validates contact exists and fetches required data for opportunity creation
//  */
// private async validateAndFetchContact(
//     contactId: string,
//     companyId: string,
// ): Promise<ContactForOpportunity> {
//     const contact = await this.contactModel.findOne(
//         { _id: contactId, companyId },
//         { isBusiness: 1, lastName: 1, firstName: 1, businessName: 1, createdAt: 1, type: 1 },
//     );

//     if (!contact) {
//         throw new BadRequestException("Contact not found!");
//     }

//     return contact as ContactForOpportunity;
// }

// /**
//  * Generates PO number and opportunity number
//  */
// private async generatePOAndNumber(
//     contact: ContactForOpportunity,
//     street: string,
//     companyId: string,
//     warrantyType: boolean,
// ): Promise<{ PO: string; num: string }> {
//     const name = this.getContactDisplayName(contact);
//     const PO = createPO(name, street);
//     const num = await this.getNum(companyId, PO, warrantyType);

//     return { PO, num };
// }

// /**
//  * Gets the display name for a contact
//  */
// private getContactDisplayName(contact: ContactForOpportunity): string {
//     if (contact?.isBusiness) {
//         return contact?.businessName || "UNKN";
//     }

//     if (contact?.lastName && contact.lastName.trim() !== "") {
//         return contact.lastName;
//     }

//     return contact?.firstName || "UNKN";
// }

// /**
//  * Handles contact transfer logic when originalContact is different from contactId
//  * This occurs when an opportunity is being created for a different contact than originally specified
//  * @param createOpportunityDto - The opportunity creation data
//  * @param memberId - ID of the member performing the operation
//  * @param companyId - Company ID
//  * @param session - Database session for transaction
//  */
// private async handleContactTransfer(
//     createOpportunityDto: OpportunityCreationData,
//     memberId: string,
//     companyId: string,
//     session: any,
// ) {
//     const { originalContact, contactId, currentDate } = createOpportunityDto;

//     if (!originalContact || originalContact === contactId) {
//         return;
//     }

//     // Check if target contact already has active leads
//     const existingActiveLead = await this.leadModel.findOne(
//         { contactId, companyId, status: "active", deleted: false },
//         { _id: 1 },
//     );

//     // Transfer the only active lead (if it exists) from originalContact to contactId
//     await this.leadModel.updateOne(
//         { contactId: originalContact, companyId, status: "active", deleted: false },
//         {
//             $set: {
//                 contactId,
//                 ...(existingActiveLead && {
//                     status: "invalid",
//                     invalidLeadReason: "Current Client",
//                     invalidDate: currentDate,
//                     invalidBy: memberId,
//                 }),
//             },
//         },
//         { session },
//     );

//     // Add originalContact as a linked contact to contactId (if not already linked)
//     await this.contactModel.updateOne(
//         { _id: contactId, companyId, "linkedContacts.id": { $ne: originalContact } },
//         { $push: { linkedContacts: { id: originalContact, relationship: "Other" } } },
//         { session },
//     );
// }

// /**
//  * Handles lead management logic including creation and conversion
//  */
// private async handleLeadManagement(
//     createOpportunityDto: any,
//     opportunityId: string,
//     memberId: string,
//     companyId: string,
//     session: any,
// ) {
//     const { createLead } = createOpportunityDto;

//     // Fetch default stage data for New Lead
//     const stageData = await this.getDefaultLeadStage(companyId);

//     if (createLead) {
//         await this.handleNewLeadCreation(
//             createOpportunityDto,
//             opportunityId,
//             memberId,
//             companyId,
//             stageData,
//             session,
//         );
//     } else {
//         await this.handleExistingLeadConversion(
//             createOpportunityDto,
//             opportunityId,
//             memberId,
//             companyId,
//             stageData,
//             session,
//         );
//     }
// }

// /**
//  * Gets the default lead stage for the company
//  */
// private async getDefaultLeadStage(companyId: string) {
//     const stageData = await this.crmStageModel.findOne(
//         {
//             companyId,
//             deleted: false,
//             stageGroup: StageGroupEnum.Leads,
//             sequence: 1,
//         },
//         { _id: 1, defaultCsrId: 1 },
//     );

//     if (!stageData) {
//         throw new BadRequestException("Default lead stage not found for company");
//     }

//     return stageData;
// }

// /**
//  * Handles creation of new lead with opportunity
//  */
// private async handleNewLeadCreation(
//     createOpportunityDto: any,
//     opportunityId: string,
//     memberId: string,
//     companyId: string,
//     stageData: any,
//     session: any,
// ) {
//     const { leadId, contactId, currentDate } = createOpportunityDto;

//     // Mark existing lead as lost if provided
//     if (leadId) {
//         await this.leadModel.updateOne(
//             { _id: leadId, companyId },
//             {
//                 $set: {
//                     lostReason: "New Opportunity created",
//                     lostDate: currentDate,
//                     status: "lost",
//                     lostBy: memberId,
//                 },
//             },
//             { session },
//         );
//     }

//     // Create new lead
//     const leadData = {
//         contactId,
//         companyId,
//         createdBy: memberId,
//         status: "converted",
//         stageId: stageData._id,
//         newLeadDate: createOpportunityDto?.newLeadDate,
//         csrId: createOpportunityDto?.csrId || stageData.defaultCsrId,
//         leadSourceId: createOpportunityDto?.leadSourceId,
//         campaignId: createOpportunityDto?.campaignId,
//         workType: createOpportunityDto?.oppType,
//         referredBy: createOpportunityDto?.referredBy,
//         oppId: opportunityId,
//         oppDate: currentDate,
//     };

//     const createdLead = new this.leadModel(leadData);
//     await createdLead.save({ session });

//     // Add activity log
//     await this.addContactActivity(
//         contactId,
//         companyId,
//         "created a New Lead with Opportunity",
//         memberId,
//         currentDate,
//         session,
//     );
// }

// /**
//  * Handles conversion of existing lead to opportunity
//  */
// private async handleExistingLeadConversion(
//     createOpportunityDto: any,
//     opportunityId: string,
//     memberId: string,
//     companyId: string,
//     stageData: any,
//     session: any,
// ) {
//     const { leadId, contactId, currentDate } = createOpportunityDto;

//     if (leadId) {
//         // Convert specific lead
//         const { modifiedCount } = await this.leadModel.updateOne(
//             { _id: leadId, status: "active", companyId, oppId: { $exists: false } },
//             { $set: { oppId: opportunityId, oppDate: currentDate, status: "converted" } },
//             { session },
//         );

//         if (modifiedCount > 0) {
//             await this.addContactActivity(
//                 contactId,
//                 companyId,
//                 "converted Lead to Opportunity",
//                 memberId,
//                 currentDate,
//                 session,
//             );
//         }
//     } else {
//         // Find and convert most recent active lead or create new one
//         const leadData = await this.leadModel.findOne(
//             { contactId, companyId, deleted: false, status: "active" },
//             { sort: { newLeadDate: -1 } },
//         );

//         if (leadData) {
//             await this.leadModel.updateOne(
//                 { _id: leadData._id },
//                 { $set: { oppId: opportunityId, oppDate: currentDate, status: "converted" } },
//                 { session },
//             );
//         } else {
//             // Create new lead if none exists
//             const leadData = {
//                 contactId,
//                 companyId,
//                 createdBy: memberId,
//                 status: "converted",
//                 stageId: stageData._id,
//                 newLeadDate: createOpportunityDto?.newLeadDate,
//                 csrId: createOpportunityDto?.csrId || stageData.defaultCsrId,
//                 leadSourceId: createOpportunityDto?.leadSourceId,
//                 campaignId: createOpportunityDto?.campaignId,
//                 workType: createOpportunityDto?.oppType,
//                 referredBy: createOpportunityDto?.referredBy,
//                 oppId: opportunityId,
//                 oppDate: currentDate,
//             };

//             const newLead = new this.leadModel(leadData);
//             await newLead.save({ session });
//         }
//     }
// }

// /**
//  * Adds activity log entry for contact
//  */
// private async addContactActivity(
//     contactId: string,
//     companyId: string,
//     body: string,
//     createdBy: string,
//     createdAt: string,
//     session: any,
// ) {
//     await this.activityModel.updateOne(
//         {
//             moduleId: contactId,
//             moduleType: "contact",
//             companyId,
//         },
//         {
//             $push: {
//                 activities: {
//                     _id: randomUUID(),
//                     body,
//                     createdBy,
//                     createdAt: new Date(createdAt).toISOString(),
//                 },
//             },
//         },
//         { session },
//     );
// }

// /**
//  * Creates opportunity activity log
//  */
// private async createOpportunityActivity(
//     opportunityId: string,
//     createdBy: string,
//     companyId: string,
//     currentDate: string,
//     session: any,
// ) {
//     const createdOppActivity = new this.activityModel({
//         moduleId: opportunityId,
//         moduleType: "opportunity",
//         companyId,
//         activities: [
//             {
//                 _id: randomUUID(),
//                 body: "created New Opportunity",
//                 createdBy,
//                 createdAt: new Date(currentDate).toISOString(),
//             },
//         ],
//     });

//     await createdOppActivity.save({ session });
// }

// /**
//  * Creates the main opportunity record with all required data
//  */
// private async createOpportunityRecord(
//     opportunityId: string,
//     PO: string,
//     num: string,
//     createOpportunityDto: any,
//     companyId: string,
//     warrantyType: boolean,
//     session: any,
// ) {
//     const { memberId, currentDate } = createOpportunityDto;

//     // Build comments array
//     const comments = this.buildOpportunityComments(createOpportunityDto, memberId, currentDate);

//     // Get tax jurisdiction if only one exists for the state
//     const taxJurisdiction = await this.getTaxJurisdiction(companyId, createOpportunityDto.state);

//     // Build checkpoint activity
//     const checkpointActivity = this.buildCheckpointActivity(createOpportunityDto, warrantyType);

//     // Clean up DTO for opportunity creation
//     const cleanedDto = this.cleanDtoForOpportunityCreation(createOpportunityDto);

//     // Set sale date for warranty type
//     if (warrantyType) {
//         cleanedDto.saleDate = cleanedDto.oppDate;
//     }

//     const opportunityData = {
//         _id: opportunityId,
//         PO,
//         num,
//         type: "new",
//         status: "active",
//         companyId,
//         salesPersonHistory: cleanedDto?.salesPerson ? [cleanedDto.salesPerson] : [],
//         checkpointActivity,
//         taxJurisdiction,
//         comments,
//         warrantyType,
//         ...cleanedDto,
//     };

//     const createdOpportunity = new this.opportunityModel(opportunityData);
//     await createdOpportunity.save({ session });

//     return createdOpportunity;
// }

// /**
//  * Builds comments array for opportunity
//  */
// private buildOpportunityComments(createOpportunityDto: any, memberId: string, currentDate: string) {
//     const comments = [...(createOpportunityDto?.comments || [])];

//     if (createOpportunityDto?.oppNotes) {
//         comments.push({
//             _id: randomUUID(),
//             type: "notes",
//             createdBy: memberId,
//             body: createOpportunityDto.oppNotes,
//             createdAt: new Date(currentDate).toISOString(),
//         });
//     }

//     return comments;
// }

// /**
//  * Gets tax jurisdiction if only one exists for the state
//  */
// private async getTaxJurisdiction(companyId: string, state: string) {
//     if (!state) return undefined;

//     const allTax = await this.taxModel.find({
//         companyId,
//         state,
//         deleted: false,
//     });

//     return allTax.length === 1 ? allTax[0]._id : undefined;
// }

// /**
//  * Builds checkpoint activity object
//  */
// private buildCheckpointActivity(createOpportunityDto: any, warrantyType: boolean) {
//     const checkpointActivity: any = {
//         newLeadDate: {
//             created: new Date(createOpportunityDto.newLeadDate).toISOString(),
//         },
//         oppDate: {
//             created: new Date(createOpportunityDto.oppDate).toISOString(),
//         },
//     };

//     if (warrantyType) {
//         checkpointActivity.saleDate = {
//             created: new Date(createOpportunityDto.oppDate).toISOString(),
//         };
//     }

//     if (createOpportunityDto?.needsAssessmentDate) {
//         checkpointActivity.needsAssessmentDate = {
//             created: new Date(createOpportunityDto.needsAssessmentDate).toISOString(),
//         };
//     }

//     return checkpointActivity;
// }

// /**
//  * Cleans DTO for opportunity creation by removing unwanted fields
//  */
// private cleanDtoForOpportunityCreation(createOpportunityDto: any) {
//     const cleaned = { ...createOpportunityDto };
//     delete cleaned.comments;
//     delete cleaned.originalContact;
//     return cleaned;
// }

// /**
//  * Updates contact information after opportunity creation
//  */
// private async updateContactAfterOpportunityCreation(
//     contact: any,
//     createOpportunityDto: any,
//     companyId: string,
//     session: any,
// ) {
//     const { createLead, leadId } = createOpportunityDto;

//     const contactUpdate = {
//         ...(contact.type !== ContactTypeEnum.CLIENT && { type: ContactTypeEnum.PROSPECT }),
//         ...(!createLead &&
//             leadId &&
//             createOpportunityDto?.oppType &&
//             contact.type !== ContactTypeEnum.CLIENT && { workType: createOpportunityDto.oppType }),
//     };

//     // Update lead source if contact is less than 14 days old
//     this.updateContactLeadSourceIfRecent(contact, createOpportunityDto, contactUpdate);

//     await this.contactModel.updateOne(
//         { _id: createOpportunityDto.contactId },
//         { $set: contactUpdate },
//         { session },
//     );
// }

// /**
//  * Updates contact lead source if contact is recent (less than 14 days old)
//  */
// private updateContactLeadSourceIfRecent(contact: any, createOpportunityDto: any, contactUpdate: any) {
//     if (!contact || !createOpportunityDto?.leadSourceId) {
//         return;
//     }

//     const contactAge = new Date().getTime() - new Date(contact.createdAt).getTime();
//     const daysOld = contactAge / (1000 * 60 * 60 * 24);

//     if (daysOld <= 14) {
//         contactUpdate.leadSourceId = createOpportunityDto.leadSourceId;

//         if (
//             createOpportunityDto?.campaignId &&
//             !["", undefined, null].includes(createOpportunityDto.campaignId)
//         ) {
//             contactUpdate.campaignId = createOpportunityDto.campaignId;
//         }
//     }
// }

// /**
//  * Updates contact counts asynchronously
//  */
// private updateContactCounts(createOpportunityDto: any, companyId: string) {
//     const { contactId, referredBy, createLead } = createOpportunityDto;

//     const countsToUpdate = {
//         opportunitiesCount: 1,
//         ...(referredBy && referredBy !== "unknown" && { referralsCount: 1 }),
//         ...(createLead && { leadsCount: 1 }),
//     };

//     // Execute asynchronously without blocking
//     this.contactsService
//         .increaseContactCounts(contactId, companyId, countsToUpdate)
//         .catch((error) => console.error("Error updating contact counts:", error));
// }

// /**
//  * Handles errors during opportunity creation
//  */
// private handleCreateOpportunityError(error: any): never {
//     if (error instanceof HttpException) {
//         throw error;
//     }

//     console.error("Error creating opportunity:", error);
//     throw new InternalServerErrorException(
//         `Failed to create opportunity: ${error.message || "Unknown error"}`,
//     );
// }
